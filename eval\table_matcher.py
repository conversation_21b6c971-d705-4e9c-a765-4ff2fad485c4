"""
表格匹配器 - 基于表格前5行内容的NED匹配算法
"""

import re
from typing import List, Dict, Tuple, Optional
from bs4 import BeautifulSoup
try:
    from Levenshtein import distance as levenshtein_distance
    LEVENSHTEIN_AVAILABLE = True
except ImportError:
    LEVENSHTEIN_AVAILABLE = False


class TableMatcher:
    """表格匹配器，基于表格内容进行智能匹配"""
    
    def __init__(self, similarity_threshold: float = 0.6, max_preview_rows: int = 5):
        """
        初始化表格匹配器
        
        Args:
            similarity_threshold: 相似度阈值，低于此值的不进行匹配
            max_preview_rows: 用于匹配的最大行数
        """
        self.similarity_threshold = similarity_threshold
        self.max_preview_rows = max_preview_rows
    
    def extract_table_preview(self, table_html: str) -> List[List[str]]:
        """
        提取表格前N行数据用于匹配
        
        Args:
            table_html: 表格HTML字符串
            
        Returns:
            表格前N行的数据，每行是一个字符串列表
        """
        try:
            soup = BeautifulSoup(table_html, 'html.parser')
            table = soup.find('table')
            
            if not table:
                return []
            
            rows = []
            for i, tr in enumerate(table.find_all('tr')):
                if i >= self.max_preview_rows:
                    break
                
                row = []
                for cell in tr.find_all(['td', 'th']):
                    cell_text = cell.get_text(strip=True)
                    # 标准化单元格内容
                    cell_text = self._normalize_cell_content(cell_text)
                    row.append(cell_text)
                
                if row:  # 只添加非空行
                    rows.append(row)
            
            return rows
            
        except Exception as e:
            print(f"提取表格预览失败: {e}")
            return []
    
    def _normalize_cell_content(self, cell_text: str) -> str:
        """
        标准化单元格内容
        
        Args:
            cell_text: 原始单元格文本
            
        Returns:
            标准化后的文本
        """
        # 移除多余的空白字符
        cell_text = re.sub(r'\s+', ' ', cell_text.strip())
        
        # 转换为小写（可选，根据需要调整）
        # cell_text = cell_text.lower()
        
        return cell_text
    
    def table_preview_to_text(self, table_preview: List[List[str]]) -> str:
        """
        将表格预览转换为文本字符串用于NED计算
        
        Args:
            table_preview: 表格预览数据
            
        Returns:
            表格的文本表示
        """
        if not table_preview:
            return ""
        
        # 将每行用换行符连接，每个单元格用制表符连接
        text_rows = []
        for row in table_preview:
            text_rows.append('\t'.join(row))
        
        return '\n'.join(text_rows)
    
    def compute_table_similarity(self, table1_preview: List[List[str]], 
                                table2_preview: List[List[str]]) -> float:
        """
        计算两个表格预览的相似度
        
        Args:
            table1_preview: 第一个表格的预览数据
            table2_preview: 第二个表格的预览数据
            
        Returns:
            相似度分数 (0-1之间)
        """
        # 转换为文本
        text1 = self.table_preview_to_text(table1_preview)
        text2 = self.table_preview_to_text(table2_preview)
        
        if not text1 and not text2:
            return 1.0
        
        if not text1 or not text2:
            return 0.0
        
        # 计算NED相似度
        return self._compute_ned_similarity(text1, text2)
    
    def _compute_ned_similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文本的NED相似度
        
        Args:
            text1: 第一个文本
            text2: 第二个文本
            
        Returns:
            NED相似度分数
        """
        if LEVENSHTEIN_AVAILABLE:
            edit_distance = levenshtein_distance(text1, text2)
        else:
            edit_distance = self._compute_edit_distance(text1, text2)
        
        max_length = max(len(text1), len(text2))
        if max_length == 0:
            return 1.0
        
        similarity = 1.0 - (edit_distance / max_length)
        return max(0.0, min(1.0, similarity))
    
    def _compute_edit_distance(self, s1: str, s2: str) -> int:
        """
        计算编辑距离（当Levenshtein库不可用时的备用实现）
        
        Args:
            s1: 第一个字符串
            s2: 第二个字符串
            
        Returns:
            编辑距离
        """
        m, n = len(s1), len(s2)
        
        # 创建DP表
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        # 初始化边界条件
        for i in range(m + 1):
            dp[i][0] = i
        for j in range(n + 1):
            dp[0][j] = j
        
        # 填充DP表
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if s1[i-1] == s2[j-1]:
                    dp[i][j] = dp[i-1][j-1]
                else:
                    dp[i][j] = 1 + min(dp[i-1][j],      # 删除
                                      dp[i][j-1],      # 插入
                                      dp[i-1][j-1])    # 替换
        
        return dp[m][n]
    
    def find_best_matches(self, truth_tables: List[str], 
                         output_tables: List[str]) -> List[Tuple[int, int, float]]:
        """
        找到最佳匹配对
        
        Args:
            truth_tables: 真实表格列表
            output_tables: 输出表格列表
            
        Returns:
            匹配对列表 [(truth_idx, output_idx, similarity), ...]
        """
        matches = []
        
        # 提取所有表格的预览
        truth_previews = [self.extract_table_preview(table) for table in truth_tables]
        output_previews = [self.extract_table_preview(table) for table in output_tables]
        
        # 计算所有可能的相似度
        similarities = []
        for i, truth_preview in enumerate(truth_previews):
            for j, output_preview in enumerate(output_previews):
                similarity = self.compute_table_similarity(truth_preview, output_preview)
                if similarity >= self.similarity_threshold:
                    similarities.append((i, j, similarity))
        
        # 按相似度降序排序
        similarities.sort(key=lambda x: x[2], reverse=True)
        
        # 贪心匹配，确保每个表格最多匹配一次
        used_truth = set()
        used_output = set()
        
        for truth_idx, output_idx, similarity in similarities:
            if truth_idx not in used_truth and output_idx not in used_output:
                matches.append((truth_idx, output_idx, similarity))
                used_truth.add(truth_idx)
                used_output.add(output_idx)
        
        return matches
    
    def match_tables(self, truth_tables: List[str], 
                    output_tables: List[str]) -> Dict[int, Optional[int]]:
        """
        匹配表格，返回映射关系
        
        Args:
            truth_tables: 真实表格列表
            output_tables: 输出表格列表
            
        Returns:
            映射字典 {truth_idx: output_idx or None}
        """
        matches = self.find_best_matches(truth_tables, output_tables)
        
        # 创建映射字典
        mapping = {}
        for i in range(len(truth_tables)):
            mapping[i] = None
        
        for truth_idx, output_idx, _ in matches:
            mapping[truth_idx] = output_idx
        
        return mapping


def main():
    """测试函数"""
    matcher = TableMatcher()
    
    # 测试表格
    table1 = """
    <table>
        <tr><th>姓名</th><th>年龄</th><th>城市</th></tr>
        <tr><td>张三</td><td>25</td><td>北京</td></tr>
        <tr><td>李四</td><td>30</td><td>上海</td></tr>
    </table>
    """
    
    table2 = """
    <table>
        <tr><th>姓名</th><th>年龄</th><th>城市</th></tr>
        <tr><td>张三</td><td>25</td><td>北京</td></tr>
        <tr><td>李四</td><td>30</td><td>上海</td></tr>
        <tr><td>王五</td><td>28</td><td>广州</td></tr>
    </table>
    """
    
    table3 = """
    <table>
        <tr><th>产品</th><th>价格</th></tr>
        <tr><td>苹果</td><td>5.0</td></tr>
        <tr><td>香蕉</td><td>3.0</td></tr>
    </table>
    """
    
    truth_tables = [table1, table3]
    output_tables = [table2, table3]
    
    mapping = matcher.match_tables(truth_tables, output_tables)
    print("匹配结果:", mapping)
    
    matches = matcher.find_best_matches(truth_tables, output_tables)
    print("详细匹配:", matches)


if __name__ == "__main__":
    main()
