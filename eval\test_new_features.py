"""
测试新功能：markdown语法清理和智能表格匹配
"""

import tempfile
from pathlib import Path

from .content_extractor import ContentExtractor
from .table_matcher import TableMatcher
from .evaluator import Evaluator


def test_markdown_syntax_removal():
    """测试markdown语法移除功能"""
    print("=== 测试markdown语法移除功能 ===")

    extractor = ContentExtractor()

    # 测试文本包含各种markdown语法
    test_text = """
# 这是一级标题

## 这是二级标题

这是一段**粗体文本**和*斜体文本*。

这里有一个[链接](https://example.com)和一个![图片](image.jpg)。

- 这是无序列表项1
- 这是无序列表项2

1. 这是有序列表项1
2. 这是有序列表项2

> 这是引用文本

这里有`行内代码`。

```python
# 这是代码块
print("Hello World")
```

---

~~删除线文本~~和==高亮文本==。

<table>
<tr><th>表格</th><th>列</th></tr>
<tr><td>数据</td><td>值</td></tr>
</table>
"""

    # 提取并清理文本
    result = extractor.extract_from_content(test_text)
    cleaned_text = result["text_content"]

    print("原始文本长度:", len(test_text))
    print("清理后文本长度:", len(cleaned_text))
    print("\n清理后的文本:")
    print(repr(cleaned_text))  # 使用repr显示特殊字符

    # 验证markdown符号被移除
    markdown_symbols = [
        "#",
        "**",
        "*",
        "[",
        "]",
        "(",
        ")",
        "`",
        ">",
        "---",
        "~~",
        "==",
        "|",
    ]
    remaining_symbols = [
        symbol for symbol in markdown_symbols if symbol in cleaned_text
    ]

    if remaining_symbols:
        print(f"\n警告: 仍有markdown符号未被清理: {remaining_symbols}")
    else:
        print("\n✓ 所有markdown符号已被成功清理")

    return len(remaining_symbols) == 0


def test_table_matching():
    """测试智能表格匹配功能"""
    print("\n=== 测试智能表格匹配功能 ===")

    matcher = TableMatcher(similarity_threshold=0.6)

    # 创建测试表格
    table1 = """
    <table>
        <tr><th>姓名</th><th>年龄</th><th>城市</th></tr>
        <tr><td>张三</td><td>25</td><td>北京</td></tr>
        <tr><td>李四</td><td>30</td><td>上海</td></tr>
    </table>
    """

    table2 = """
    <table>
        <tr><th>产品</th><th>价格</th></tr>
        <tr><td>苹果</td><td>5.0</td></tr>
        <tr><td>香蕉</td><td>3.0</td></tr>
    </table>
    """

    table3 = """
    <table>
        <tr><th>姓名</th><th>年龄</th><th>城市</th></tr>
        <tr><td>张三</td><td>25</td><td>北京</td></tr>
        <tr><td>李四</td><td>30</td><td>上海</td></tr>
        <tr><td>王五</td><td>28</td><td>广州</td></tr>
    </table>
    """

    # 测试场景：truth有table1和table2，output有table3和table2（顺序不同）
    truth_tables = [table1, table2]
    output_tables = [table3, table2]

    print("Truth表格数量:", len(truth_tables))
    print("Output表格数量:", len(output_tables))

    # 执行匹配
    mapping = matcher.match_tables(truth_tables, output_tables)
    matches = matcher.find_best_matches(truth_tables, output_tables)

    print("\n匹配结果:")
    for truth_idx, output_idx in mapping.items():
        if output_idx is not None:
            print(f"  Truth表格{truth_idx} -> Output表格{output_idx}")
        else:
            print(f"  Truth表格{truth_idx} -> 未匹配")

    print("\n详细匹配信息:")
    for truth_idx, output_idx, similarity in matches:
        print(f"  Truth{truth_idx} <-> Output{output_idx}, 相似度: {similarity:.3f}")

    # 验证匹配结果
    # table1和table3相似度高(0.743 > 0.6)，应该匹配
    # table2完全匹配，相似度1.0，应该匹配
    expected_matches = 2
    actual_matches = len([idx for idx in mapping.values() if idx is not None])

    print(f"\n期望匹配数: {expected_matches}, 实际匹配数: {actual_matches}")

    # 验证具体匹配关系
    # Truth表格1(table2)应该匹配到Output表格1(table2)
    table2_matched_correctly = mapping.get(1) == 1

    success = actual_matches == expected_matches and table2_matched_correctly
    if success:
        print("✓ 表格匹配测试通过")
    else:
        print("✗ 表格匹配测试失败")

    return success


def test_evaluator_integration():
    """测试评估器集成功能"""
    print("\n=== 测试评估器集成功能 ===")

    # 创建临时文件
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # 创建truth文件
        truth_content = """
# 测试文档

这是一段**测试文本**。

<table>
    <tr><th>姓名</th><th>年龄</th></tr>
    <tr><td>张三</td><td>25</td></tr>
</table>

这是另一段文本。

<table>
    <tr><th>产品</th><th>价格</th></tr>
    <tr><td>苹果</td><td>5.0</td></tr>
</table>
"""

        # 创建output文件（表格顺序不同）
        output_content = """
# 测试文档

这是一段测试文本。

<table>
    <tr><th>产品</th><th>价格</th></tr>
    <tr><td>苹果</td><td>5.0</td></tr>
</table>

这是另一段文本。

<table>
    <tr><th>姓名</th><th>年龄</th></tr>
    <tr><td>张三</td><td>25</td></tr>
</table>
"""

        truth_file = temp_path / "truth.md"
        output_file = temp_path / "output.md"

        truth_file.write_text(truth_content, encoding="utf-8")
        output_file.write_text(output_content, encoding="utf-8")

        # 测试智能匹配
        evaluator_smart = Evaluator(enable_smart_table_matching=True)
        result_smart = evaluator_smart.evaluate_file_pair(truth_file, output_file)

        # 测试顺序匹配
        evaluator_sequential = Evaluator(enable_smart_table_matching=False)
        result_sequential = evaluator_sequential.evaluate_file_pair(
            truth_file, output_file
        )

        print("智能匹配结果:")
        print(f"  文本NED分数: {result_smart['text_evaluation']['ned_score']:.3f}")
        print(
            f"  表格平均TEDS分数: {result_smart['table_evaluation']['avg_teds_score']:.3f}"
        )
        print(
            f"  匹配方法: {result_smart['table_evaluation'].get('matching_method', 'unknown')}"
        )

        print("\n顺序匹配结果:")
        print(f"  文本NED分数: {result_sequential['text_evaluation']['ned_score']:.3f}")
        print(
            f"  表格平均TEDS分数: {result_sequential['table_evaluation']['avg_teds_score']:.3f}"
        )
        print(
            f"  匹配方法: {result_sequential['table_evaluation'].get('matching_method', 'unknown')}"
        )

        # 验证智能匹配应该有更好的表格分数
        smart_table_score = result_smart["table_evaluation"]["avg_teds_score"]
        sequential_table_score = result_sequential["table_evaluation"]["avg_teds_score"]

        print(f"\n智能匹配表格分数: {smart_table_score:.3f}")
        print(f"顺序匹配表格分数: {sequential_table_score:.3f}")

        success = smart_table_score >= sequential_table_score
        if success:
            print("✓ 评估器集成测试通过")
        else:
            print("✗ 评估器集成测试失败")

        return success


def main():
    """运行所有测试"""
    print("开始测试新功能...\n")

    results = []

    # 运行各项测试
    results.append(test_markdown_syntax_removal())
    results.append(test_table_matching())
    results.append(test_evaluator_integration())

    # 汇总结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print(f"  Markdown语法清理: {'✓' if results[0] else '✗'}")
    print(f"  智能表格匹配: {'✓' if results[1] else '✗'}")
    print(f"  评估器集成: {'✓' if results[2] else '✗'}")

    total_passed = sum(results)
    print(f"\n总计: {total_passed}/{len(results)} 项测试通过")

    if total_passed == len(results):
        print("🎉 所有测试通过！新功能工作正常。")
    else:
        print("⚠️  部分测试失败，请检查实现。")

    return total_passed == len(results)


if __name__ == "__main__":
    main()
