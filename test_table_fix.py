#!/usr/bin/env python3
"""
测试新的表格修复功能
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from processors.content_processor import ContentProcessor
from processors.ai_services import LLMService
from processors.table_merger import TableMerger
from utils.logger import get_logger

logger = get_logger(__name__)


def test_llm_table_fix():
    """测试LLM表格修复功能"""
    print("=" * 50)
    print("测试LLM表格修复功能")
    print("=" * 50)

    # 创建测试表格数据
    test_tables = [
        """<table>
<tr><th>姓名</th><th>年龄</th><th>城市</th></tr>
<tr><td>张三</td><td>25</td><td></td></tr>
</table>""",
        """<table>
<tr><td></td><td></td><td>北京</td></tr>
<tr><td>李四</td><td>30</td><td>上海</td></tr>
</table>""",
    ]

    try:
        ai_service = LLMService()
        result = ai_service.fix_merged_tables(test_tables)

        print("输入表格:")
        for i, table in enumerate(test_tables, 1):
            print(f"表格 {i}:")
            print(table)
            print()

        print("修复结果:")
        print(result)
        print()

        return result is not None and len(result.strip()) > 0

    except Exception as e:
        print(f"测试失败: {e}")
        return False


def test_table_merger_llm_fix():
    """测试表格合并器的LLM修复功能"""
    print("=" * 50)
    print("测试表格合并器LLM修复功能")
    print("=" * 50)

    # 创建测试表格数据
    test_table_data = [
        {
            "type": "table",
            "table_body": """<table>
<tr><th>产品名称</th><th>价格</th><th>库存</th></tr>
<tr><td>苹果</td><td>5.0</td><td></td></tr>
</table>""",
            "table_caption": ["表格1"],
            "table_footnote": [],
            "img_path": "",
            "page_idx": 1,
        },
        {
            "type": "table",
            "table_body": """<table>
<tr><td></td><td></td><td>100</td></tr>
<tr><td>香蕉</td><td>3.0</td><td>200</td></tr>
</table>""",
            "table_caption": ["表格2"],
            "table_footnote": [],
            "img_path": "",
            "page_idx": 2,
        },
    ]

    try:
        table_merger = TableMerger()
        result = table_merger.merge_tables_with_llm_fix(test_table_data)

        print("输入表格数据:")
        for i, table_data in enumerate(test_table_data, 1):
            print(f"表格 {i}:")
            print(f"  Body: {table_data['table_body']}")
            print(f"  Caption: {table_data['table_caption']}")
            print()

        print("合并结果:")
        print(f"  Type: {result.get('type')}")
        print(f"  Body: {result.get('table_body')}")
        print(f"  Caption: {result.get('table_caption')}")
        print(f"  Page: {result.get('page_idx')}")
        print()

        return result is not None and result.get("table_body")

    except Exception as e:
        print(f"测试失败: {e}")
        return False


def test_content_processor_llm_mode():
    """测试内容处理器的LLM模式"""
    print("=" * 50)
    print("测试内容处理器LLM模式")
    print("=" * 50)

    # 创建测试表格数据
    prev_table = {
        "type": "table",
        "table_body": """<table>
<tr><th>学生</th><th>数学</th><th>英语</th></tr>
<tr><td>小明</td><td>90</td><td></td></tr>
</table>""",
        "table_caption": [],
        "table_footnote": [],
        "img_path": "",
        "page_idx": 1,
    }

    next_table = {
        "type": "table",
        "table_body": """<table>
<tr><td></td><td></td><td>85</td></tr>
<tr><td>小红</td><td>95</td><td>88</td></tr>
</table>""",
        "table_caption": [],
        "table_footnote": [],
        "img_path": "",
        "page_idx": 2,
    }

    try:
        # 测试LLM模式
        processor_llm = ContentProcessor(use_llm_fix=True)
        result_llm = processor_llm._handle_table_continuation(prev_table, next_table)

        print("LLM模式结果:")
        if result_llm:
            print(f"  Body: {result_llm.get('table_body')}")
        else:
            print("  失败")
        print()

        # 测试传统模式
        processor_traditional = ContentProcessor(use_llm_fix=False)
        result_traditional = processor_traditional._handle_table_continuation(
            prev_table, next_table
        )

        print("传统模式结果:")
        if result_traditional:
            print(f"  Body: {result_traditional.get('table_body')}")
        else:
            print("  失败")
        print()

        return result_llm is not None and result_traditional is not None

    except Exception as e:
        print(f"测试失败: {e}")
        return False


def test_multiple_table_continuation():
    """测试多表格续表功能"""
    print("=" * 50)
    print("测试多表格续表功能")
    print("=" * 50)

    # 创建3个测试表格
    tables = [
        {
            "type": "table",
            "table_body": """<table>
<tr><th>编号</th><th>名称</th><th>类型</th></tr>
<tr><td>001</td><td>产品A</td><td></td></tr>
</table>""",
            "table_caption": ["产品表"],
            "table_footnote": [],
            "img_path": "",
            "page_idx": 1,
        },
        {
            "type": "table",
            "table_body": """<table>
<tr><td></td><td></td><td>电子</td></tr>
<tr><td>002</td><td>产品B</td><td>服装</td></tr>
</table>""",
            "table_caption": [],
            "table_footnote": [],
            "img_path": "",
            "page_idx": 2,
        },
        {
            "type": "table",
            "table_body": """<table>
<tr><td>003</td><td>产品C</td><td>食品</td></tr>
</table>""",
            "table_caption": [],
            "table_footnote": ["注：产品信息"],
            "img_path": "",
            "page_idx": 3,
        },
    ]

    try:
        processor = ContentProcessor(use_llm_fix=True)
        result = processor.handle_multiple_table_continuation(tables)

        print("输入表格:")
        for i, table in enumerate(tables, 1):
            print(f"表格 {i}: {table['table_body']}")
        print()

        print("合并结果:")
        if result:
            print(f"  Body: {result.get('table_body')}")
            print(f"  Caption: {result.get('table_caption')}")
            print(f"  Footnote: {result.get('table_footnote')}")
        else:
            print("  失败")
        print()

        return result is not None

    except Exception as e:
        print(f"测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试新的表格修复功能...")
    print()

    tests = [
        ("LLM表格修复", test_llm_table_fix),
        ("表格合并器LLM修复", test_table_merger_llm_fix),
        ("内容处理器LLM模式", test_content_processor_llm_mode),
        ("多表格续表", test_multiple_table_continuation),
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"✓ {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"✗ {test_name}: 异常 - {e}")
        print()

    print("=" * 50)
    print("测试总结:")
    print("=" * 50)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")

    print(f"\n总计: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
