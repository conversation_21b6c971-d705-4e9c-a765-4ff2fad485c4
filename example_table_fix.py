#!/usr/bin/env python3
"""
表格修复功能使用示例
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from processors.content_processor import ContentProcessor
from processors.ai_services import LLMService
from processors.table_merger import TableMerger


def example_basic_usage():
    """基本使用示例"""
    print("=" * 60)
    print("基本使用示例：修复两个断裂的表格")
    print("=" * 60)
    
    # 模拟跨页断裂的表格数据
    table1 = {
        "type": "table",
        "table_body": """<table>
<tr><th>员工姓名</th><th>部门</th><th>工资</th></tr>
<tr><td>张三</td><td>技术部</td><td></td></tr>
<tr><td>李四</td><td>销售部</td><td>8000</td></tr>
</table>""",
        "table_caption": ["员工信息表"],
        "table_footnote": [],
        "img_path": "",
        "page_idx": 1
    }
    
    table2 = {
        "type": "table", 
        "table_body": """<table>
<tr><td></td><td></td><td>12000</td></tr>
<tr><td>王五</td><td>人事部</td><td>9000</td></tr>
</table>""",
        "table_caption": [],
        "table_footnote": ["注：工资单位为人民币"],
        "img_path": "",
        "page_idx": 2
    }
    
    print("原始表格1:")
    print(table1["table_body"])
    print("\n原始表格2:")
    print(table2["table_body"])
    
    # 使用新的LLM修复功能
    processor = ContentProcessor(use_llm_fix=True)
    result = processor._handle_table_continuation(table1, table2)
    
    print("\n修复后的表格:")
    if result:
        print(result["table_body"])
        print(f"\n标题: {result['table_caption']}")
        print(f"脚注: {result['table_footnote']}")
    else:
        print("修复失败")


def example_multiple_tables():
    """多表格修复示例"""
    print("\n" + "=" * 60)
    print("多表格修复示例：修复三个连续的断裂表格")
    print("=" * 60)
    
    # 模拟三个连续的断裂表格
    tables = [
        {
            "type": "table",
            "table_body": """<table>
<tr><th>订单号</th><th>客户</th><th>金额</th><th>状态</th></tr>
<tr><td>001</td><td>客户A</td><td>1000</td><td></td></tr>
</table>""",
            "table_caption": ["订单统计表"],
            "table_footnote": [],
            "img_path": "",
            "page_idx": 1
        },
        {
            "type": "table",
            "table_body": """<table>
<tr><td></td><td></td><td></td><td>已完成</td></tr>
<tr><td>002</td><td>客户B</td><td>1500</td><td>进行中</td></tr>
<tr><td>003</td><td></td><td></td><td></td></tr>
</table>""",
            "table_caption": [],
            "table_footnote": [],
            "img_path": "",
            "page_idx": 2
        },
        {
            "type": "table",
            "table_body": """<table>
<tr><td></td><td>客户C</td><td>2000</td><td>已完成</td></tr>
<tr><td>004</td><td>客户D</td><td>800</td><td>待处理</td></tr>
</table>""",
            "table_caption": [],
            "table_footnote": ["统计时间：2024年1月"],
            "img_path": "",
            "page_idx": 3
        }
    ]
    
    print("原始表格片段:")
    for i, table in enumerate(tables, 1):
        print(f"\n表格 {i}:")
        print(table["table_body"])
    
    # 使用多表格修复功能
    processor = ContentProcessor(use_llm_fix=True)
    result = processor.handle_multiple_table_continuation(tables)
    
    print("\n修复后的完整表格:")
    if result:
        print(result["table_body"])
        print(f"\n标题: {result['table_caption']}")
        print(f"脚注: {result['table_footnote']}")
    else:
        print("修复失败")


def example_direct_llm_fix():
    """直接使用LLM修复示例"""
    print("\n" + "=" * 60)
    print("直接LLM修复示例：只使用HTML字符串")
    print("=" * 60)
    
    # 直接使用HTML字符串
    broken_tables = [
        """<table>
<tr><th>课程</th><th>学分</th><th>成绩</th></tr>
<tr><td>数学</td><td>4</td><td></td></tr>
<tr><td>英语</td><td></td><td></td></tr>
</table>""",
        """<table>
<tr><td></td><td></td><td>95</td></tr>
<tr><td></td><td>3</td><td>88</td></tr>
<tr><td>物理</td><td>4</td><td>92</td></tr>
</table>"""
    ]
    
    print("断裂的表格片段:")
    for i, table in enumerate(broken_tables, 1):
        print(f"\n片段 {i}:")
        print(table)
    
    # 直接使用LLM服务
    llm_service = LLMService()
    fixed_table = llm_service.fix_merged_tables(broken_tables)
    
    print("\n修复后的表格:")
    print(fixed_table)


def example_comparison():
    """新旧方法对比示例"""
    print("\n" + "=" * 60)
    print("新旧方法对比示例")
    print("=" * 60)
    
    table1 = {
        "type": "table",
        "table_body": """<table>
<tr><th>产品</th><th>价格</th><th>库存</th></tr>
<tr><td>笔记本</td><td>5000</td><td></td></tr>
</table>""",
        "table_caption": [],
        "table_footnote": [],
        "img_path": "",
        "page_idx": 1
    }
    
    table2 = {
        "type": "table",
        "table_body": """<table>
<tr><td></td><td></td><td>50</td></tr>
<tr><td>手机</td><td>3000</td><td>100</td></tr>
</table>""",
        "table_caption": [],
        "table_footnote": [],
        "img_path": "",
        "page_idx": 2
    }
    
    print("原始表格:")
    print("表格1:", table1["table_body"])
    print("表格2:", table2["table_body"])
    
    # 新方法：LLM直接修复
    print("\n使用新方法（LLM直接修复）:")
    processor_new = ContentProcessor(use_llm_fix=True)
    result_new = processor_new._handle_table_continuation(table1, table2)
    if result_new:
        print(result_new["table_body"])
    
    # 旧方法：先判断再合并
    print("\n使用旧方法（先判断再合并）:")
    processor_old = ContentProcessor(use_llm_fix=False)
    result_old = processor_old._handle_table_continuation(table1, table2)
    if result_old:
        print(result_old["table_body"])


def main():
    """主函数"""
    print("表格修复功能使用示例")
    print("本示例展示了如何使用新的LLM表格修复功能")
    
    try:
        example_basic_usage()
        example_multiple_tables()
        example_direct_llm_fix()
        example_comparison()
        
        print("\n" + "=" * 60)
        print("所有示例运行完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"运行示例时出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
