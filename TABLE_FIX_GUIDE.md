# MinerU 表格修复功能使用指南

## 🆕 新功能概述

本项目新增了基于LLM的智能表格修复功能，可以直接修复因页面分割而断裂的HTML表格，无需先判断是否需要合并。

## 功能特点

### ✨ LLM直接修复模式
- **智能合并**：LLM直接分析多个表格片段，自动识别并修复断裂
- **结构修复**：自动修正rowspan、colspan属性
- **重复表头处理**：自动删除重复的表头
- **多表格支持**：支持同时修复2个以上的表格片段

### 🔄 传统判断模式（保留）
- **兼容性**：保留原有的LLM判断+规则合并逻辑
- **降级策略**：当LLM修复失败时自动回退到传统方法

## 使用方法

### 1. 基本使用

```python
from processors.content_processor import ContentProcessor

# 创建处理器（默认使用LLM修复模式）
processor = ContentProcessor(use_llm_fix=True)

# 处理两个表格
result = processor._handle_table_continuation(table1, table2)
```

### 2. 多表格修复

```python
# 处理多个连续的表格
tables = [table1, table2, table3]
result = processor.handle_multiple_table_continuation(tables)
```

### 3. 直接LLM修复

```python
from processors.ai_services import LLMService

llm_service = LLMService()
html_tables = ["<table>...</table>", "<table>...</table>"]
fixed_html = llm_service.fix_merged_tables(html_tables)
```

### 4. 使用传统模式

```python
# 使用传统的判断+合并模式
processor = ContentProcessor(use_llm_fix=False)
result = processor._handle_table_continuation(table1, table2)
```

## 修复规则

LLM修复遵循以下规则：

1. **行合并**：将第二个表格的第一行与第一个表格的最后一行进行比对，若它们是同一逻辑行的延续，则将它们合并
2. **rowspan**：识别并合并相邻行中同一列内内容相同的单元格
3. **colspan**：检查表头和数据行的单元格数量，确保它们与完整的表格结构匹配，并使用 colspan 填充缺失的单元格；每行的 colspan 总数应该相同
4. **重复表头**：如果表头有重复，那么应该删去

## 示例

### 输入（断裂的表格）

表格1：
```html
<table>
<tr><th>姓名</th><th>年龄</th><th>城市</th></tr>
<tr><td>张三</td><td>25</td><td></td></tr>
</table>
```

表格2：
```html
<table>
<tr><td></td><td></td><td>北京</td></tr>
<tr><td>李四</td><td>30</td><td>上海</td></tr>
</table>
```

### 输出（修复后的表格）

```html
<table>
<tr><th>姓名</th><th>年龄</th><th>城市</th></tr>
<tr><td>张三</td><td>25</td><td>北京</td></tr>
<tr><td>李四</td><td>30</td><td>上海</td></tr>
</table>
```

## 测试

运行测试脚本验证功能：

```bash
python test_table_fix.py
```

运行示例代码：

```bash
python example_table_fix.py
```

## 技术实现

### 核心组件

1. **prompt文件**：`prompts/table_fix_prompts.md` - 定义LLM修复的提示词
2. **AI服务**：`processors/ai_services.py` - 新增`fix_merged_tables()`方法
3. **表格合并器**：`processors/table_merger.py` - 新增`merge_tables_with_llm_fix()`方法
4. **内容处理器**：`processors/content_processor.py` - 集成新的修复逻辑
5. **提示词加载器**：`utils/prompt_loader.py` - 支持新的表格修复提示词

### 降级策略

- LLM修复失败时自动回退到传统合并方法
- 传统合并失败时返回第一个表格
- 确保系统的稳定性和可靠性

## 配置选项

### ContentProcessor 参数

- `use_llm_fix=True`：使用LLM直接修复（推荐）
- `use_llm_fix=False`：使用传统判断+合并模式

### 环境要求

- 需要配置LLM API密钥
- 确保网络连接正常
- Python 3.7+

## 性能对比

| 特性 | 传统模式 | LLM修复模式 |
|------|----------|-------------|
| 准确性 | 中等 | 高 |
| 复杂表格处理 | 有限 | 优秀 |
| 多表格支持 | 逐个处理 | 批量处理 |
| 结构修复 | 基础 | 智能 |
| API调用次数 | 每对表格1次 | 每批表格1次 |

## 注意事项

1. **API成本**：LLM修复模式会调用外部API，产生费用
2. **网络依赖**：需要稳定的网络连接
3. **响应时间**：LLM处理可能需要几秒钟时间
4. **降级机制**：确保在LLM不可用时系统仍能正常工作

## 故障排除

### 常见问题

1. **LLM调用失败**
   - 检查API密钥配置
   - 确认网络连接
   - 查看错误日志

2. **修复结果不理想**
   - 检查输入表格格式
   - 尝试调整prompt
   - 使用传统模式作为对比

3. **性能问题**
   - 考虑批量处理多个表格
   - 调整超时设置
   - 监控API使用量

### 日志查看

系统会记录详细的处理日志，包括：
- LLM调用记录
- 修复前后的表格对比
- 错误信息和降级策略执行情况

## 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 新增LLM直接修复功能
- ✅ 支持多表格批量修复
- ✅ 完善的降级策略
- ✅ 全面的测试覆盖
