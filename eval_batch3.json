总文件数: 50
成功评估: 50
评估失败: 0

综合评估分数:
  平均分数: 0.9589
  最高分数: 1.0000
  最低分数: 0.8728

文本评估 (NED):
  平均分数: 0.9741
  最高分数: 1.0000
  最低分数: 0.8698

表格评估 (TEDS):
  平均分数: 0.9437
  最高分数: 1.0000
  最低分数: 0.8133

{
  "total_files": 50,
  "individual_results": [
    {
      "files": {
        "truth": "truth\\688475-萤石网络-1223072954.md",
        "output": "output\\688475-萤石网络-1223072954.md"
      },
      "text_evaluation": {
        "ned_score": 0.9718614718614719,
        "edit_distance": 39,
        "char_accuracy": 0.018037518037518036,
        "length_stats": {
          "truth_length": 1386,
          "output_length": 1347,
          "length_ratio": 0.9718614718614719
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 8,
        "output_table_count": 7,
        "avg_teds_score": 0.8132869722700502,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.8820183215130024,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.6242774566473989,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": null,
            "teds_score": 0.0,
            "matched": false
          },
          {
            "truth_index": 5,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 1,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.8925742220657611,
        "text_score": 0.9718614718614719,
        "table_score": 0.8132869722700502,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1429,
          "table_count": 8
        },
        "output": {
          "text_length": 1404,
          "table_count": 7
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688478-晶升股份-1219921010.md",
        "output": "output\\688478-晶升股份-1219921010.md"
      },
      "text_evaluation": {
        "ned_score": 0.9984447900466563,
        "edit_distance": 2,
        "char_accuracy": 0.9984447900466563,
        "length_stats": {
          "truth_length": 1286,
          "output_length": 1286,
          "length_ratio": 1.0
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 7,
        "avg_teds_score": 0.9688461827190151,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.9090909090909091,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.8728323699421965,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9836454863828357,
        "text_score": 0.9984447900466563,
        "table_score": 0.9688461827190151,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1343,
          "table_count": 7
        },
        "output": {
          "text_length": 1343,
          "table_count": 7
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688485-九州一轨-1219921063.md",
        "output": "output\\688485-九州一轨-1219921063.md"
      },
      "text_evaluation": {
        "ned_score": 0.9531123686337915,
        "edit_distance": 58,
        "char_accuracy": 0.04203718674211803,
        "length_stats": {
          "truth_length": 1237,
          "output_length": 1209,
          "length_ratio": 0.9773645917542442
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 8,
        "avg_teds_score": 0.8693916746854422,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.7241379310344828,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 0.9411764705882353,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.9337016574585635,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.48672566371681414,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 1,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9112520216596168,
        "text_score": 0.9531123686337915,
        "table_score": 0.8693916746854422,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1284,
          "table_count": 7
        },
        "output": {
          "text_length": 1268,
          "table_count": 8
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688508-芯朋微-1223112154.md",
        "output": "output\\688508-芯朋微-1223112154.md"
      },
      "text_evaluation": {
        "ned_score": 1.0,
        "edit_distance": 0,
        "char_accuracy": 1.0,
        "length_stats": {
          "truth_length": 1255,
          "output_length": 1255,
          "length_ratio": 1.0
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 7,
        "avg_teds_score": 0.9707971586424625,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.7955801104972375,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9853985793212312,
        "text_score": 1.0,
        "table_score": 0.9707971586424625,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1310,
          "table_count": 7
        },
        "output": {
          "text_length": 1310,
          "table_count": 7
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688519-南亚新材-1223110524.md",
        "output": "output\\688519-南亚新材-1223110524.md"
      },
      "text_evaluation": {
        "ned_score": 0.9884881043745204,
        "edit_distance": 15,
        "char_accuracy": 0.9091614906832298,
        "length_stats": {
          "truth_length": 1288,
          "output_length": 1303,
          "length_ratio": 1.0116459627329193
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 8,
        "avg_teds_score": 0.9010764160388182,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.8208092485549133,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.48672566371681414,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 1,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9447822602066693,
        "text_score": 0.9884881043745204,
        "table_score": 0.9010764160388182,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1347,
          "table_count": 7
        },
        "output": {
          "text_length": 1363,
          "table_count": 8
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688523-航天环宇-1219921072.md",
        "output": "output\\688523-航天环宇-1219921072.md"
      },
      "text_evaluation": {
        "ned_score": 0.9550089982003599,
        "edit_distance": 75,
        "char_accuracy": 0.02399520095980804,
        "length_stats": {
          "truth_length": 1667,
          "output_length": 1592,
          "length_ratio": 0.9550089982003599
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 7,
        "avg_teds_score": 0.9193936534151232,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.7,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.735755573905863,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9372013258077416,
        "text_score": 0.9550089982003599,
        "table_score": 0.9193936534151232,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1712,
          "table_count": 7
        },
        "output": {
          "text_length": 1641,
          "table_count": 7
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688525-佰维存储-1219921125.md",
        "output": "output\\688525-佰维存储-1219921125.md"
      },
      "text_evaluation": {
        "ned_score": 0.9675925925925926,
        "edit_distance": 49,
        "char_accuracy": 0.036375661375661374,
        "length_stats": {
          "truth_length": 1512,
          "output_length": 1463,
          "length_ratio": 0.9675925925925926
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 7,
        "avg_teds_score": 0.9424840110056056,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.6818181818181819,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 0.9271978021978022,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.9883720930232558,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9550383017990991,
        "text_score": 0.9675925925925926,
        "table_score": 0.9424840110056056,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1559,
          "table_count": 7
        },
        "output": {
          "text_length": 1522,
          "table_count": 7
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688533-上声电子-1219924101.md",
        "output": "output\\688533-上声电子-1219924101.md"
      },
      "text_evaluation": {
        "ned_score": 0.9359079704190633,
        "edit_distance": 78,
        "char_accuracy": 0.07066557107641742,
        "length_stats": {
          "truth_length": 1217,
          "output_length": 1199,
          "length_ratio": 0.9852095316351684
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 9,
        "avg_teds_score": 0.9180571231929358,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.9454545454545454,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.9942196531791907,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.48672566371681414,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 2,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9269825468059996,
        "text_score": 0.9359079704190633,
        "table_score": 0.9180571231929358,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1262,
          "table_count": 7
        },
        "output": {
          "text_length": 1255,
          "table_count": 9
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688559-海目星-1219921162.md",
        "output": "output\\688559-海目星-1219921162.md"
      },
      "text_evaluation": {
        "ned_score": 0.9872013651877133,
        "edit_distance": 15,
        "char_accuracy": 0.8928262748487468,
        "length_stats": {
          "truth_length": 1157,
          "output_length": 1172,
          "length_ratio": 1.0129645635263613
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 8,
        "avg_teds_score": 0.9220681823051705,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.728813559322034,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.7256637168141593,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 1,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9546347737464419,
        "text_score": 0.9872013651877133,
        "table_score": 0.9220681823051705,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1207,
          "table_count": 7
        },
        "output": {
          "text_length": 1223,
          "table_count": 8
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688560-明冠新材-1219921172.md",
        "output": "output\\688560-明冠新材-1219921172.md"
      },
      "text_evaluation": {
        "ned_score": 0.9763406940063092,
        "edit_distance": 30,
        "char_accuracy": 0.9103392568659128,
        "length_stats": {
          "truth_length": 1238,
          "output_length": 1268,
          "length_ratio": 1.024232633279483
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 8,
        "output_table_count": 10,
        "avg_teds_score": 0.9050723807936384,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.9272727272727272,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 0.8947368421052632,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.9453551912568307,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 0.4732142857142857,
            "matched": true
          }
        ],
        "matched_pairs": 8,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 2,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9407065373999738,
        "text_score": 0.9763406940063092,
        "table_score": 0.9050723807936384,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1287,
          "table_count": 8
        },
        "output": {
          "text_length": 1319,
          "table_count": 10
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688565-力源科技-1219921173.md",
        "output": "output\\688565-力源科技-1219921173.md"
      },
      "text_evaluation": {
        "ned_score": 0.9765441751368257,
        "edit_distance": 30,
        "char_accuracy": 0.9071257005604484,
        "length_stats": {
          "truth_length": 1249,
          "output_length": 1279,
          "length_ratio": 1.0240192153722978
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 9,
        "avg_teds_score": 0.9191956632534865,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.9476439790575917,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.48672566371681414,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 2,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9478699191951561,
        "text_score": 0.9765441751368257,
        "table_score": 0.9191956632534865,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1294,
          "table_count": 7
        },
        "output": {
          "text_length": 1326,
          "table_count": 9
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688566-吉贝尔-1223096199.md",
        "output": "output\\688566-吉贝尔-1223096199.md"
      },
      "text_evaluation": {
        "ned_score": 1.0,
        "edit_distance": 0,
        "char_accuracy": 1.0,
        "length_stats": {
          "truth_length": 1135,
          "output_length": 1135,
          "length_ratio": 1.0
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 7,
        "avg_teds_score": 0.9628406275805119,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.7398843930635839,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.981420313790256,
        "text_score": 1.0,
        "table_score": 0.9628406275805119,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1186,
          "table_count": 7
        },
        "output": {
          "text_length": 1186,
          "table_count": 7
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688577-浙海德曼-1219921204.md",
        "output": "output\\688577-浙海德曼-1219921204.md"
      },
      "text_evaluation": {
        "ned_score": 0.9769938650306749,
        "edit_distance": 45,
        "char_accuracy": 0.80429094714809,
        "length_stats": {
          "truth_length": 1911,
          "output_length": 1956,
          "length_ratio": 1.0235478806907379
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 11,
        "output_table_count": 14,
        "avg_teds_score": 0.9043110520125441,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.8823529411764706,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 0.9383429672447013,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 0.48672566371681414,
            "matched": true
          },
          {
            "truth_index": 8,
            "output_index": 10,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 9,
            "output_index": 11,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 10,
            "output_index": 12,
            "teds_score": 0.64,
            "matched": true
          }
        ],
        "matched_pairs": 11,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 3,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9406524585216095,
        "text_score": 0.9769938650306749,
        "table_score": 0.9043110520125441,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1978,
          "table_count": 11
        },
        "output": {
          "text_length": 2026,
          "table_count": 14
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688590-新致软件-1219921213.md",
        "output": "output\\688590-新致软件-1219921213.md"
      },
      "text_evaluation": {
        "ned_score": 0.9808306709265175,
        "edit_distance": 30,
        "char_accuracy": 0.9250814332247557,
        "length_stats": {
          "truth_length": 1535,
          "output_length": 1565,
          "length_ratio": 1.019543973941368
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 9,
        "avg_teds_score": 0.8192848550133979,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.6454545454545455,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.8055888223552894,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.28395061728395066,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 2,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9000577629699578,
        "text_score": 0.9808306709265175,
        "table_score": 0.8192848550133979,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1591,
          "table_count": 7
        },
        "output": {
          "text_length": 1623,
          "table_count": 9
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688591-泰凌微-1219921247.md",
        "output": "output\\688591-泰凌微-1219921247.md"
      },
      "text_evaluation": {
        "ned_score": 1.0,
        "edit_distance": 0,
        "char_accuracy": 1.0,
        "length_stats": {
          "truth_length": 1299,
          "output_length": 1299,
          "length_ratio": 1.0
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 7,
        "avg_teds_score": 0.9755573284284339,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.8909090909090909,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.9379922080899465,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.987778664214217,
        "text_score": 1.0,
        "table_score": 0.9755573284284339,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1355,
          "table_count": 7
        },
        "output": {
          "text_length": 1355,
          "table_count": 7
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688596-正帆科技-1219924135.md",
        "output": "output\\688596-正帆科技-1219924135.md"
      },
      "text_evaluation": {
        "ned_score": 0.9817407181984176,
        "edit_distance": 30,
        "char_accuracy": 0.9057656540607564,
        "length_stats": {
          "truth_length": 1613,
          "output_length": 1643,
          "length_ratio": 1.018598884066956
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 9,
        "avg_teds_score": 0.8855128672137822,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.711864406779661,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.48672566371681414,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 2,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9336267927060999,
        "text_score": 0.9817407181984176,
        "table_score": 0.8855128672137822,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1673,
          "table_count": 7
        },
        "output": {
          "text_length": 1705,
          "table_count": 9
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688620-安凯微-1219921279.md",
        "output": "output\\688620-安凯微-1219921279.md"
      },
      "text_evaluation": {
        "ned_score": 0.9573367808661927,
        "edit_distance": 66,
        "char_accuracy": 0.03425985778926956,
        "length_stats": {
          "truth_length": 1547,
          "output_length": 1481,
          "length_ratio": 0.9573367808661927
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 8,
        "output_table_count": 8,
        "avg_teds_score": 0.9642642667160155,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.7627118644067796,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 0.962962962962963,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 0.9884393063583815,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 8,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9608005237911041,
        "text_score": 0.9573367808661927,
        "table_score": 0.9642642667160155,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1596,
          "table_count": 8
        },
        "output": {
          "text_length": 1539,
          "table_count": 8
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688623-双元科技-1219924211.md",
        "output": "output\\688623-双元科技-1219924211.md"
      },
      "text_evaluation": {
        "ned_score": 1.0,
        "edit_distance": 0,
        "char_accuracy": 1.0,
        "length_stats": {
          "truth_length": 1159,
          "output_length": 1159,
          "length_ratio": 1.0
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 7,
        "avg_teds_score": 0.9755724044741385,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.9272727272727272,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.9017341040462428,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9877862022370693,
        "text_score": 1.0,
        "table_score": 0.9755724044741385,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1216,
          "table_count": 7
        },
        "output": {
          "text_length": 1216,
          "table_count": 7
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688646-逸飞激光-1219921284.md",
        "output": "output\\688646-逸飞激光-1219921284.md"
      },
      "text_evaluation": {
        "ned_score": 0.9830412662521198,
        "edit_distance": 30,
        "char_accuracy": 0.9378953421506613,
        "length_stats": {
          "truth_length": 1739,
          "output_length": 1769,
          "length_ratio": 1.0172512938470386
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 9,
        "avg_teds_score": 0.9042443463743786,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.8545454545454545,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.9884393063583815,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.48672566371681414,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 2,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9436428063132491,
        "text_score": 0.9830412662521198,
        "table_score": 0.9042443463743786,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1790,
          "table_count": 7
        },
        "output": {
          "text_length": 1822,
          "table_count": 9
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688651-盛邦安全-1219960345.md",
        "output": "output\\688651-盛邦安全-1219960345.md"
      },
      "text_evaluation": {
        "ned_score": 0.9773926149208741,
        "edit_distance": 30,
        "char_accuracy": 0.9013107170393215,
        "length_stats": {
          "truth_length": 1297,
          "output_length": 1327,
          "length_ratio": 1.023130300693909
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 9,
        "avg_teds_score": 0.8728289409705339,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.9272727272727272,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.6958041958041958,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.48672566371681414,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 2,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.925110777945704,
        "text_score": 0.9773926149208741,
        "table_score": 0.8728289409705339,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1348,
          "table_count": 7
        },
        "output": {
          "text_length": 1380,
          "table_count": 9
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688658-悦康药业-1219924235.md",
        "output": "output\\688658-悦康药业-1219924235.md"
      },
      "text_evaluation": {
        "ned_score": 1.0,
        "edit_distance": 0,
        "char_accuracy": 1.0,
        "length_stats": {
          "truth_length": 1132,
          "output_length": 1132,
          "length_ratio": 1.0
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 8,
        "output_table_count": 8,
        "avg_teds_score": 0.9498437388065047,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.8909090909090909,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.7819148936170213,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 0.9259259259259259,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 8,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9749218694032524,
        "text_score": 1.0,
        "table_score": 0.9498437388065047,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1183,
          "table_count": 8
        },
        "output": {
          "text_length": 1183,
          "table_count": 8
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688660-电气风电-1219921308.md",
        "output": "output\\688660-电气风电-1219921308.md"
      },
      "text_evaluation": {
        "ned_score": 0.9478005865102639,
        "edit_distance": 89,
        "char_accuracy": 0.4350247524752475,
        "length_stats": {
          "truth_length": 1616,
          "output_length": 1705,
          "length_ratio": 1.0550742574257426
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 12,
        "output_table_count": 12,
        "avg_teds_score": 0.9922872340425531,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 0.9574468085106383,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 0.95,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 8,
            "output_index": 8,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 9,
            "output_index": 9,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 10,
            "output_index": 10,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 11,
            "output_index": 11,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 12,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9700439102764085,
        "text_score": 0.9478005865102639,
        "table_score": 0.9922872340425531,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1688,
          "table_count": 12
        },
        "output": {
          "text_length": 1781,
          "table_count": 12
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688665-四方光电-1219924254.md",
        "output": "output\\688665-四方光电-1219924254.md"
      },
      "text_evaluation": {
        "ned_score": 0.9899159663865547,
        "edit_distance": 30,
        "char_accuracy": 0.9612903225806452,
        "length_stats": {
          "truth_length": 2945,
          "output_length": 2975,
          "length_ratio": 1.0101867572156198
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 9,
        "output_table_count": 11,
        "avg_teds_score": 0.8896326816169263,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.6818181818181819,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 0.838150289017341,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 8,
            "output_index": 8,
            "teds_score": 0.48672566371681414,
            "matched": true
          }
        ],
        "matched_pairs": 9,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 2,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9397743240017404,
        "text_score": 0.9899159663865547,
        "table_score": 0.8896326816169263,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 3057,
          "table_count": 9
        },
        "output": {
          "text_length": 3089,
          "table_count": 11
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688671-碧兴物联-1219925927.md",
        "output": "output\\688671-碧兴物联-1219925927.md"
      },
      "text_evaluation": {
        "ned_score": 0.9748110831234257,
        "edit_distance": 30,
        "char_accuracy": 0.8992248062015504,
        "length_stats": {
          "truth_length": 1161,
          "output_length": 1191,
          "length_ratio": 1.0258397932816536
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 9,
        "output_table_count": 11,
        "avg_teds_score": 0.9214829764730015,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.8181818181818181,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.9884393063583815,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 8,
            "output_index": 8,
            "teds_score": 0.48672566371681414,
            "matched": true
          }
        ],
        "matched_pairs": 9,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 2,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9481470297982135,
        "text_score": 0.9748110831234257,
        "table_score": 0.9214829764730015,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1211,
          "table_count": 9
        },
        "output": {
          "text_length": 1243,
          "table_count": 11
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688679-通源环境-1219921316.md",
        "output": "output\\688679-通源环境-1219921316.md"
      },
      "text_evaluation": {
        "ned_score": 1.0,
        "edit_distance": 0,
        "char_accuracy": 1.0,
        "length_stats": {
          "truth_length": 1119,
          "output_length": 1119,
          "length_ratio": 1.0
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 7,
        "avg_teds_score": 0.9154281433521063,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.711864406779661,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.6961325966850829,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9577140716760532,
        "text_score": 1.0,
        "table_score": 0.9154281433521063,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1165,
          "table_count": 7
        },
        "output": {
          "text_length": 1165,
          "table_count": 7
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688681-科汇股份-1219921319.md",
        "output": "output\\688681-科汇股份-1219921319.md"
      },
      "text_evaluation": {
        "ned_score": 1.0,
        "edit_distance": 0,
        "char_accuracy": 1.0,
        "length_stats": {
          "truth_length": 1183,
          "output_length": 1183,
          "length_ratio": 1.0
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 6,
        "output_table_count": 6,
        "avg_teds_score": 0.9818181818181818,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.8909090909090909,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 6,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.990909090909091,
        "text_score": 1.0,
        "table_score": 0.9818181818181818,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1236,
          "table_count": 6
        },
        "output": {
          "text_length": 1236,
          "table_count": 6
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688685-迈信林-1220543285.md",
        "output": "output\\688685-迈信林-1220543285.md"
      },
      "text_evaluation": {
        "ned_score": 0.9481627296587927,
        "edit_distance": 79,
        "char_accuracy": 0.03740157480314961,
        "length_stats": {
          "truth_length": 1524,
          "output_length": 1445,
          "length_ratio": 0.9481627296587927
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 7,
        "avg_teds_score": 0.9585809731144165,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.8909090909090909,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.8191577208918249,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9533718513866045,
        "text_score": 0.9481627296587927,
        "table_score": 0.9585809731144165,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1571,
          "table_count": 7
        },
        "output": {
          "text_length": 1496,
          "table_count": 7
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688686-奥普特-1219921391.md",
        "output": "output\\688686-奥普特-1219921391.md"
      },
      "text_evaluation": {
        "ned_score": 0.9171717171717172,
        "edit_distance": 123,
        "char_accuracy": 0.039057239057239054,
        "length_stats": {
          "truth_length": 1485,
          "output_length": 1482,
          "length_ratio": 0.997979797979798
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 10,
        "output_table_count": 14,
        "avg_teds_score": 0.8284657644323212,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.8545454545454545,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.654054054054054,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 0.982665805340224,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.48672566371681414,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 9,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 8,
            "output_index": 10,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 9,
            "output_index": 11,
            "teds_score": 0.30666666666666664,
            "matched": true
          }
        ],
        "matched_pairs": 10,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 4,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.8728187408020192,
        "text_score": 0.9171717171717172,
        "table_score": 0.8284657644323212,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1543,
          "table_count": 10
        },
        "output": {
          "text_length": 1548,
          "table_count": 14
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688687-凯因科技-1222078031.md",
        "output": "output\\688687-凯因科技-1222078031.md"
      },
      "text_evaluation": {
        "ned_score": 0.9864376130198915,
        "edit_distance": 15,
        "char_accuracy": 0.8964252978918423,
        "length_stats": {
          "truth_length": 1091,
          "output_length": 1106,
          "length_ratio": 1.013748854262145
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 8,
        "avg_teds_score": 0.9452246868176071,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.8909090909090909,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.7256637168141593,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 1,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9658311499187493,
        "text_score": 0.9864376130198915,
        "table_score": 0.9452246868176071,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1139,
          "table_count": 7
        },
        "output": {
          "text_length": 1155,
          "table_count": 8
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688698-伟创电气-1219921396.md",
        "output": "output\\688698-伟创电气-1219921396.md"
      },
      "text_evaluation": {
        "ned_score": 1.0,
        "edit_distance": 0,
        "char_accuracy": 1.0,
        "length_stats": {
          "truth_length": 1494,
          "output_length": 1494,
          "length_ratio": 1.0
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 8,
        "avg_teds_score": 0.9162158700970332,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.8727272727272728,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.7885714285714286,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.752212389380531,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 1,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9581079350485167,
        "text_score": 1.0,
        "table_score": 0.9162158700970332,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1552,
          "table_count": 7
        },
        "output": {
          "text_length": 1552,
          "table_count": 8
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688709-成都华微-1219921400.md",
        "output": "output\\688709-成都华微-1219921400.md"
      },
      "text_evaluation": {
        "ned_score": 0.9409317803660566,
        "edit_distance": 71,
        "char_accuracy": 0.039101497504159734,
        "length_stats": {
          "truth_length": 1202,
          "output_length": 1191,
          "length_ratio": 0.9908485856905158
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 9,
        "avg_teds_score": 0.8611268085165218,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.8727272727272728,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.6971428571428571,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 0.9712918660287081,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.48672566371681414,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 2,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9010292944412892,
        "text_score": 0.9409317803660566,
        "table_score": 0.8611268085165218,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1247,
          "table_count": 7
        },
        "output": {
          "text_length": 1245,
          "table_count": 9
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688786-悦安新材-1219921413.md",
        "output": "output\\688786-悦安新材-1219921413.md"
      },
      "text_evaluation": {
        "ned_score": 0.9628691983122363,
        "edit_distance": 44,
        "char_accuracy": 0.04472573839662447,
        "length_stats": {
          "truth_length": 1185,
          "output_length": 1141,
          "length_ratio": 0.9628691983122363
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 7,
        "output_table_count": 7,
        "avg_teds_score": 0.9307498324839365,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.8181818181818181,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 0.962962962962963,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 0.7341040462427746,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 7,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9468095153980864,
        "text_score": 0.9628691983122363,
        "table_score": 0.9307498324839365,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1230,
          "table_count": 7
        },
        "output": {
          "text_length": 1195,
          "table_count": 7
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688789-宏华数科-1219921416.md",
        "output": "output\\688789-宏华数科-1219921416.md"
      },
      "text_evaluation": {
        "ned_score": 0.9491425192193969,
        "edit_distance": 86,
        "char_accuracy": 0.03015966883500887,
        "length_stats": {
          "truth_length": 1691,
          "output_length": 1605,
          "length_ratio": 0.9491425192193969
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 9,
        "output_table_count": 9,
        "avg_teds_score": 0.9611268926172168,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.8727272727272728,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 0.791907514450867,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.9855072463768116,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 8,
            "output_index": 8,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 9,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9551347059183068,
        "text_score": 0.9491425192193969,
        "table_score": 0.9611268926172168,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1739,
          "table_count": 9
        },
        "output": {
          "text_length": 1661,
          "table_count": 9
        }
      }
    },
    {
      "files": {
        "truth": "truth\\688981-中芯国际-1220022807.md",
        "output": "output\\688981-中芯国际-1220022807.md"
      },
      "text_evaluation": {
        "ned_score": 0.9748982360922659,
        "edit_distance": 74,
        "char_accuracy": 0.025440976933514246,
        "length_stats": {
          "truth_length": 2948,
          "output_length": 2874,
          "length_ratio": 0.9748982360922659
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 17,
        "output_table_count": 17,
        "avg_teds_score": 0.9901014079863522,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 8,
            "output_index": 8,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 9,
            "output_index": 9,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 10,
            "output_index": 10,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 11,
            "output_index": 11,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 12,
            "output_index": 12,
            "teds_score": 0.8554913294797688,
            "matched": true
          },
          {
            "truth_index": 13,
            "output_index": 13,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 14,
            "output_index": 14,
            "teds_score": 0.9855072463768116,
            "matched": true
          },
          {
            "truth_index": 15,
            "output_index": 15,
            "teds_score": 0.9907253599114064,
            "matched": true
          },
          {
            "truth_index": 16,
            "output_index": 16,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 17,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9824998220393091,
        "text_score": 0.9748982360922659,
        "table_score": 0.9901014079863522,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 3025,
          "table_count": 17
        },
        "output": {
          "text_length": 2989,
          "table_count": 17
        }
      }
    },
    {
      "files": {
        "truth": "truth\\830974-凯大催化-1221036025.md",
        "output": "output\\830974-凯大催化-1221036025.md"
      },
      "text_evaluation": {
        "ned_score": 1.0,
        "edit_distance": 0,
        "char_accuracy": 1.0,
        "length_stats": {
          "truth_length": 471,
          "output_length": 471,
          "length_ratio": 1.0
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 4,
        "output_table_count": 4,
        "avg_teds_score": 1.0,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 4,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 1.0,
        "text_score": 1.0,
        "table_score": 1.0,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 489,
          "table_count": 4
        },
        "output": {
          "text_length": 489,
          "table_count": 4
        }
      }
    },
    {
      "files": {
        "truth": "truth\\830974-凯大催化-1221036034.md",
        "output": "output\\830974-凯大催化-1221036034.md"
      },
      "text_evaluation": {
        "ned_score": 0.8698390482855143,
        "edit_distance": 186,
        "char_accuracy": 0.03507404520654715,
        "length_stats": {
          "truth_length": 1283,
          "output_length": 1429,
          "length_ratio": 1.1137957911145753
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 20,
        "output_table_count": 20,
        "avg_teds_score": 0.9788799564129379,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 0.9729592576482529,
            "matched": true
          },
          {
            "truth_index": 8,
            "output_index": 8,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 9,
            "output_index": 9,
            "teds_score": 0.631578947368421,
            "matched": true
          },
          {
            "truth_index": 10,
            "output_index": 10,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 11,
            "output_index": 11,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 12,
            "output_index": 12,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 13,
            "output_index": 13,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 14,
            "output_index": 14,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 15,
            "output_index": 15,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 16,
            "output_index": 16,
            "teds_score": 0.991358024691358,
            "matched": true
          },
          {
            "truth_index": 17,
            "output_index": 17,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 18,
            "output_index": 18,
            "teds_score": 0.9817028985507247,
            "matched": true
          },
          {
            "truth_index": 19,
            "output_index": 19,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 20,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9243595023492261,
        "text_score": 0.8698390482855143,
        "table_score": 0.9788799564129379,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1344,
          "table_count": 20
        },
        "output": {
          "text_length": 1508,
          "table_count": 20
        }
      }
    },
    {
      "files": {
        "truth": "truth\\830974-凯大催化-1221036035.md",
        "output": "output\\830974-凯大催化-1221036035.md"
      },
      "text_evaluation": {
        "ned_score": 0.9590288315629742,
        "edit_distance": 27,
        "char_accuracy": 0.05007587253414264,
        "length_stats": {
          "truth_length": 659,
          "output_length": 632,
          "length_ratio": 0.9590288315629742
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 4,
        "output_table_count": 4,
        "avg_teds_score": 0.9988562091503268,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 0.9954248366013072,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 4,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9789425203566505,
        "text_score": 0.9590288315629742,
        "table_score": 0.9988562091503268,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 676,
          "table_count": 4
        },
        "output": {
          "text_length": 650,
          "table_count": 4
        }
      }
    },
    {
      "files": {
        "truth": "truth\\830974-凯大催化-1221036039.md",
        "output": "output\\830974-凯大催化-1221036039.md"
      },
      "text_evaluation": {
        "ned_score": 0.9871078642028362,
        "edit_distance": 30,
        "char_accuracy": 0.021486892995272882,
        "length_stats": {
          "truth_length": 2327,
          "output_length": 2299,
          "length_ratio": 0.9879673399226472
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 17,
        "output_table_count": 17,
        "avg_teds_score": 0.9799209752102035,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.8451388888888889,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 8,
            "output_index": 8,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 9,
            "output_index": 9,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 10,
            "output_index": 10,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 11,
            "output_index": 11,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 12,
            "output_index": 12,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 13,
            "output_index": 13,
            "teds_score": 0.8327205882352942,
            "matched": true
          },
          {
            "truth_index": 14,
            "output_index": 14,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 15,
            "output_index": 15,
            "teds_score": 0.9807971014492753,
            "matched": true
          },
          {
            "truth_index": 16,
            "output_index": 16,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 17,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9835144197065199,
        "text_score": 0.9871078642028362,
        "table_score": 0.9799209752102035,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 2404,
          "table_count": 17
        },
        "output": {
          "text_length": 2416,
          "table_count": 17
        }
      }
    },
    {
      "files": {
        "truth": "truth\\833394-民士达-1223071507.md",
        "output": "output\\833394-民士达-1223071507.md"
      },
      "text_evaluation": {
        "ned_score": 0.9892116182572614,
        "edit_distance": 13,
        "char_accuracy": 0.3103734439834025,
        "length_stats": {
          "truth_length": 1205,
          "output_length": 1192,
          "length_ratio": 0.9892116182572614
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 17,
        "output_table_count": 17,
        "avg_teds_score": 0.9896096002145723,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 0.8584974747474747,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 8,
            "output_index": 8,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 9,
            "output_index": 9,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 10,
            "output_index": 10,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 11,
            "output_index": 11,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 12,
            "output_index": 12,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 13,
            "output_index": 13,
            "teds_score": 0.9840686274509804,
            "matched": true
          },
          {
            "truth_index": 14,
            "output_index": 14,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 15,
            "output_index": 15,
            "teds_score": 0.9807971014492753,
            "matched": true
          },
          {
            "truth_index": 16,
            "output_index": 16,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 17,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.989410609235917,
        "text_score": 0.9892116182572614,
        "table_score": 0.9896096002145723,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1259,
          "table_count": 17
        },
        "output": {
          "text_length": 1256,
          "table_count": 17
        }
      }
    },
    {
      "files": {
        "truth": "truth\\834415-恒拓开源-1220484021.md",
        "output": "output\\834415-恒拓开源-1220484021.md"
      },
      "text_evaluation": {
        "ned_score": 0.9595467284025831,
        "edit_distance": 332,
        "char_accuracy": 0.015474594858048007,
        "length_stats": {
          "truth_length": 8207,
          "output_length": 7881,
          "length_ratio": 0.9602778116242232
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 21,
        "output_table_count": 21,
        "avg_teds_score": 0.9941567030852745,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 0.8772907647907648,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 8,
            "output_index": 8,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 9,
            "output_index": 9,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 10,
            "output_index": 10,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 11,
            "output_index": 11,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 12,
            "output_index": 12,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 13,
            "output_index": 13,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 14,
            "output_index": 14,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 15,
            "output_index": 15,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 16,
            "output_index": 16,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 17,
            "output_index": 17,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 18,
            "output_index": 18,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 19,
            "output_index": 19,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 20,
            "output_index": 20,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 21,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9768517157439288,
        "text_score": 0.9595467284025831,
        "table_score": 0.9941567030852745,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 8326,
          "table_count": 21
        },
        "output": {
          "text_length": 8015,
          "table_count": 21
        }
      }
    },
    {
      "files": {
        "truth": "truth\\834415-恒拓开源-1220484023.md",
        "output": "output\\834415-恒拓开源-1220484023.md"
      },
      "text_evaluation": {
        "ned_score": 1.0,
        "edit_distance": 0,
        "char_accuracy": 1.0,
        "length_stats": {
          "truth_length": 840,
          "output_length": 840,
          "length_ratio": 1.0
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 0,
        "avg_teds_score": 1.0,
        "individual_scores": [],
        "missing_tables": 0,
        "extra_tables": 0,
        "matched_pairs": 0,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0
      },
      "overall_score": {
        "weighted_score": 1.0,
        "text_score": 1.0,
        "table_score": 1.0,
        "weights": {
          "text_weight": 1.0,
          "table_weight": 0.0
        },
        "content_type": {
          "has_text": true,
          "has_tables": false
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 854,
          "table_count": 0
        },
        "output": {
          "text_length": 854,
          "table_count": 0
        }
      }
    },
    {
      "files": {
        "truth": "truth\\836263-中航泰达-1221561283.md",
        "output": "output\\836263-中航泰达-1221561283.md"
      },
      "text_evaluation": {
        "ned_score": 0.951232302045097,
        "edit_distance": 93,
        "char_accuracy": 0.05768222338751967,
        "length_stats": {
          "truth_length": 1907,
          "output_length": 1816,
          "length_ratio": 0.9522810697430519
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 17,
        "output_table_count": 16,
        "avg_teds_score": 0.9039927316677758,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.39603960396039606,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": null,
            "teds_score": 0.0,
            "matched": false
          },
          {
            "truth_index": 8,
            "output_index": 7,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 9,
            "output_index": 8,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 10,
            "output_index": 9,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 11,
            "output_index": 10,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 12,
            "output_index": 11,
            "teds_score": 0.9863295880149813,
            "matched": true
          },
          {
            "truth_index": 13,
            "output_index": 12,
            "teds_score": 0.9855072463768116,
            "matched": true
          },
          {
            "truth_index": 14,
            "output_index": 13,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 15,
            "output_index": 14,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 16,
            "output_index": 15,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 16,
        "unmatched_truth_tables": 1,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9276125168564364,
        "text_score": 0.951232302045097,
        "table_score": 0.9039927316677758,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1971,
          "table_count": 17
        },
        "output": {
          "text_length": 1884,
          "table_count": 16
        }
      }
    },
    {
      "files": {
        "truth": "truth\\836263-中航泰达-1221561287.md",
        "output": "output\\836263-中航泰达-1221561287.md"
      },
      "text_evaluation": {
        "ned_score": 1.0,
        "edit_distance": 0,
        "char_accuracy": 1.0,
        "length_stats": {
          "truth_length": 876,
          "output_length": 876,
          "length_ratio": 1.0
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 0,
        "avg_teds_score": 1.0,
        "individual_scores": [],
        "missing_tables": 0,
        "extra_tables": 0,
        "matched_pairs": 0,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0
      },
      "overall_score": {
        "weighted_score": 1.0,
        "text_score": 1.0,
        "table_score": 1.0,
        "weights": {
          "text_weight": 1.0,
          "table_weight": 0.0
        },
        "content_type": {
          "has_text": true,
          "has_tables": false
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 888,
          "table_count": 0
        },
        "output": {
          "text_length": 888,
          "table_count": 0
        }
      }
    },
    {
      "files": {
        "truth": "truth\\838030-德众汽车-1221033113.md",
        "output": "output\\838030-德众汽车-1221033113.md"
      },
      "text_evaluation": {
        "ned_score": 1.0,
        "edit_distance": 0,
        "char_accuracy": 1.0,
        "length_stats": {
          "truth_length": 800,
          "output_length": 800,
          "length_ratio": 1.0
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 0,
        "avg_teds_score": 1.0,
        "individual_scores": [],
        "missing_tables": 0,
        "extra_tables": 0,
        "matched_pairs": 0,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0
      },
      "overall_score": {
        "weighted_score": 1.0,
        "text_score": 1.0,
        "table_score": 1.0,
        "weights": {
          "text_weight": 1.0,
          "table_weight": 0.0
        },
        "content_type": {
          "has_text": true,
          "has_tables": false
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 812,
          "table_count": 0
        },
        "output": {
          "text_length": 812,
          "table_count": 0
        }
      }
    },
    {
      "files": {
        "truth": "truth\\838030-德众汽车-1221033117.md",
        "output": "output\\838030-德众汽车-1221033117.md"
      },
      "text_evaluation": {
        "ned_score": 0.981445937300064,
        "edit_distance": 29,
        "char_accuracy": 0.023672424824056303,
        "length_stats": {
          "truth_length": 1563,
          "output_length": 1534,
          "length_ratio": 0.981445937300064
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 20,
        "output_table_count": 20,
        "avg_teds_score": 0.9912249053030303,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 0.8521022727272727,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 8,
            "output_index": 8,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 9,
            "output_index": 9,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 10,
            "output_index": 10,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 11,
            "output_index": 11,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 12,
            "output_index": 12,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 13,
            "output_index": 13,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 14,
            "output_index": 14,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 15,
            "output_index": 15,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 16,
            "output_index": 16,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 17,
            "output_index": 17,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 18,
            "output_index": 18,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 19,
            "output_index": 19,
            "teds_score": 0.9723958333333333,
            "matched": true
          }
        ],
        "matched_pairs": 20,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9863354213015472,
        "text_score": 0.981445937300064,
        "table_score": 0.9912249053030303,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1628,
          "table_count": 20
        },
        "output": {
          "text_length": 1611,
          "table_count": 20
        }
      }
    },
    {
      "files": {
        "truth": "truth\\873223-荣亿精密-1221059369.md",
        "output": "output\\873223-荣亿精密-1221059369.md"
      },
      "text_evaluation": {
        "ned_score": 0.982010582010582,
        "edit_distance": 17,
        "char_accuracy": 0.05291005291005291,
        "length_stats": {
          "truth_length": 945,
          "output_length": 928,
          "length_ratio": 0.982010582010582
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 17,
        "output_table_count": 17,
        "avg_teds_score": 0.9985475671750182,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 0.9753086419753086,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 8,
            "output_index": 8,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 9,
            "output_index": 9,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 10,
            "output_index": 10,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 11,
            "output_index": 11,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 12,
            "output_index": 12,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 13,
            "output_index": 13,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 14,
            "output_index": 14,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 15,
            "output_index": 15,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 16,
            "output_index": 16,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 17,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9902790745928001,
        "text_score": 0.982010582010582,
        "table_score": 0.9985475671750182,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 995,
          "table_count": 17
        },
        "output": {
          "text_length": 982,
          "table_count": 17
        }
      }
    },
    {
      "files": {
        "truth": "truth\\873223-荣亿精密-1221059370.md",
        "output": "output\\873223-荣亿精密-1221059370.md"
      },
      "text_evaluation": {
        "ned_score": 1.0,
        "edit_distance": 0,
        "char_accuracy": 1.0,
        "length_stats": {
          "truth_length": 1009,
          "output_length": 1009,
          "length_ratio": 1.0
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 0,
        "avg_teds_score": 1.0,
        "individual_scores": [],
        "missing_tables": 0,
        "extra_tables": 0,
        "matched_pairs": 0,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0
      },
      "overall_score": {
        "weighted_score": 1.0,
        "text_score": 1.0,
        "table_score": 1.0,
        "weights": {
          "text_weight": 1.0,
          "table_weight": 0.0
        },
        "content_type": {
          "has_text": true,
          "has_tables": false
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1026,
          "table_count": 0
        },
        "output": {
          "text_length": 1026,
          "table_count": 0
        }
      }
    },
    {
      "files": {
        "truth": "truth\\873223-荣亿精密-1221059382.md",
        "output": "output\\873223-荣亿精密-1221059382.md"
      },
      "text_evaluation": {
        "ned_score": 0.9012517385257302,
        "edit_distance": 213,
        "char_accuracy": 0.02403393025447691,
        "length_stats": {
          "truth_length": 2122,
          "output_length": 2157,
          "length_ratio": 1.0164938737040528
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 15,
        "output_table_count": 15,
        "avg_teds_score": 0.9929793765449503,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 0.9117361027197093,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 8,
            "output_index": 8,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 9,
            "output_index": 9,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 10,
            "output_index": 10,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 11,
            "output_index": 11,
            "teds_score": 0.9829545454545454,
            "matched": true
          },
          {
            "truth_index": 12,
            "output_index": 12,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 13,
            "output_index": 13,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 14,
            "output_index": 14,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 15,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9471155575353403,
        "text_score": 0.9012517385257302,
        "table_score": 0.9929793765449503,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 2180,
          "table_count": 15
        },
        "output": {
          "text_length": 2262,
          "table_count": 15
        }
      }
    },
    {
      "files": {
        "truth": "truth\\873223-荣亿精密-1221059385.md",
        "output": "output\\873223-荣亿精密-1221059385.md"
      },
      "text_evaluation": {
        "ned_score": 1.0,
        "edit_distance": 0,
        "char_accuracy": 1.0,
        "length_stats": {
          "truth_length": 1009,
          "output_length": 1009,
          "length_ratio": 1.0
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 0,
        "avg_teds_score": 1.0,
        "individual_scores": [],
        "missing_tables": 0,
        "extra_tables": 0,
        "matched_pairs": 0,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0
      },
      "overall_score": {
        "weighted_score": 1.0,
        "text_score": 1.0,
        "table_score": 1.0,
        "weights": {
          "text_weight": 1.0,
          "table_weight": 0.0
        },
        "content_type": {
          "has_text": true,
          "has_tables": false
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1026,
          "table_count": 0
        },
        "output": {
          "text_length": 1026,
          "table_count": 0
        }
      }
    },
    {
      "files": {
        "truth": "truth\\920106-林泰新材-1223113293.md",
        "output": "output\\920106-林泰新材-1223113293.md"
      },
      "text_evaluation": {
        "ned_score": 0.9653739612188366,
        "edit_distance": 50,
        "char_accuracy": 0.038781163434903045,
        "length_stats": {
          "truth_length": 1444,
          "output_length": 1394,
          "length_ratio": 0.9653739612188366
        },
        "preprocessing_config": {
          "normalize_whitespace": true,
          "ignore_case": false
        }
      },
      "table_evaluation": {
        "table_count": 18,
        "output_table_count": 18,
        "avg_teds_score": 0.9984088592306184,
        "individual_scores": [
          {
            "truth_index": 0,
            "output_index": 0,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 1,
            "output_index": 1,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 2,
            "output_index": 2,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 3,
            "output_index": 3,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 4,
            "output_index": 4,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 5,
            "output_index": 5,
            "teds_score": 0.9753086419753086,
            "matched": true
          },
          {
            "truth_index": 6,
            "output_index": 6,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 7,
            "output_index": 7,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 8,
            "output_index": 8,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 9,
            "output_index": 9,
            "teds_score": 0.9960508241758241,
            "matched": true
          },
          {
            "truth_index": 10,
            "output_index": 10,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 11,
            "output_index": 11,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 12,
            "output_index": 12,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 13,
            "output_index": 13,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 14,
            "output_index": 14,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 15,
            "output_index": 15,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 16,
            "output_index": 16,
            "teds_score": 1.0,
            "matched": true
          },
          {
            "truth_index": 17,
            "output_index": 17,
            "teds_score": 1.0,
            "matched": true
          }
        ],
        "matched_pairs": 18,
        "unmatched_truth_tables": 0,
        "unmatched_output_tables": 0,
        "matching_method": "smart_matching"
      },
      "overall_score": {
        "weighted_score": 0.9818914102247275,
        "text_score": 0.9653739612188366,
        "table_score": 0.9984088592306184,
        "weights": {
          "text_weight": 0.5,
          "table_weight": 0.5
        },
        "content_type": {
          "has_text": true,
          "has_tables": true
        }
      },
      "content_stats": {
        "truth": {
          "text_length": 1498,
          "table_count": 18
        },
        "output": {
          "text_length": 1451,
          "table_count": 18
        }
      }
    }
  ],
  "summary": {
    "valid_files": 50,
    "failed_files": 0,
    "text_metrics": {
      "avg_ned_score": 0.9741205697665236,
      "min_ned_score": 0.8698390482855143,
      "max_ned_score": 1.0
    },
    "table_metrics": {
      "avg_teds_score": 0.9436549636654975,
      "min_teds_score": 0.8132869722700502,
      "max_teds_score": 1.0
    },
    "overall_metrics": {
      "avg_weighted_score": 0.9588877667160105,
      "min_weighted_score": 0.8728187408020192,
      "max_weighted_score": 1.0
    }
  }
}