"""
文本评估指标 - 实现NED（Normalized Edit Distance）
"""

import re
from typing import Optional, Dict, Any
try:
    from Levenshtein import distance as levenshtein_distance
    LEVENSHTEIN_AVAILABLE = True
except ImportError:
    LEVENSHTEIN_AVAILABLE = False


class NED:
    """Normalized Edit Distance - 标准化编辑距离"""
    
    def __init__(self, normalize_whitespace: bool = True, ignore_case: bool = False):
        """
        初始化NED计算器
        
        Args:
            normalize_whitespace: 是否标准化空白字符
            ignore_case: 是否忽略大小写
        """
        self.normalize_whitespace = normalize_whitespace
        self.ignore_case = ignore_case
    
    def __call__(self, pred: str, gt: str) -> float:
        """
        计算两个文本之间的NED分数
        
        Args:
            pred: 预测文本
            gt: 真实标准文本
            
        Returns:
            NED分数 (0-1之间，1表示完全相同)
        """
        if not pred and not gt:
            return 1.0
        
        if not pred or not gt:
            return 0.0
        
        # 预处理文本
        pred_processed = self._preprocess_text(pred)
        gt_processed = self._preprocess_text(gt)
        
        # 计算编辑距离
        if LEVENSHTEIN_AVAILABLE:
            edit_distance = levenshtein_distance(pred_processed, gt_processed)
        else:
            edit_distance = self._compute_edit_distance(pred_processed, gt_processed)
        
        # 标准化
        max_length = max(len(pred_processed), len(gt_processed))
        if max_length == 0:
            return 1.0
        
        normalized_distance = edit_distance / max_length
        similarity = 1.0 - normalized_distance
        
        return max(0.0, min(1.0, similarity))
    
    def _preprocess_text(self, text: str) -> str:
        """
        预处理文本
        
        Args:
            text: 原始文本
            
        Returns:
            预处理后的文本
        """
        if not text:
            return ""
        
        processed = text
        
        # 忽略大小写
        if self.ignore_case:
            processed = processed.lower()
        
        # 标准化空白字符
        if self.normalize_whitespace:
            # 将多个空白字符替换为单个空格
            processed = re.sub(r'\s+', ' ', processed)
            # 移除首尾空格
            processed = processed.strip()
        
        return processed
    
    def _compute_edit_distance(self, s1: str, s2: str) -> int:
        """
        计算编辑距离（当Levenshtein库不可用时的备用实现）
        
        Args:
            s1: 字符串1
            s2: 字符串2
            
        Returns:
            编辑距离
        """
        if len(s1) < len(s2):
            return self._compute_edit_distance(s2, s1)
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]
    
    def compute_detailed_metrics(self, pred: str, gt: str) -> Dict[str, Any]:
        """
        计算详细的评估指标
        
        Args:
            pred: 预测文本
            gt: 真实标准文本
            
        Returns:
            详细的评估指标字典
        """
        # 预处理
        pred_processed = self._preprocess_text(pred)
        gt_processed = self._preprocess_text(gt)
        
        # 基本统计
        pred_length = len(pred_processed)
        gt_length = len(gt_processed)
        
        # 计算编辑距离
        if LEVENSHTEIN_AVAILABLE:
            edit_distance = levenshtein_distance(pred_processed, gt_processed)
        else:
            edit_distance = self._compute_edit_distance(pred_processed, gt_processed)
        
        # 计算NED分数
        max_length = max(pred_length, gt_length)
        ned_score = 1.0 - (edit_distance / max_length) if max_length > 0 else 1.0
        
        # 计算字符级准确率
        char_accuracy = self._compute_char_accuracy(pred_processed, gt_processed)
        
        return {
            'ned_score': max(0.0, min(1.0, ned_score)),
            'edit_distance': edit_distance,
            'pred_length': pred_length,
            'gt_length': gt_length,
            'length_ratio': pred_length / gt_length if gt_length > 0 else 0.0,
            'char_accuracy': char_accuracy,
            'preprocessing': {
                'normalize_whitespace': self.normalize_whitespace,
                'ignore_case': self.ignore_case
            }
        }
    
    def _compute_char_accuracy(self, pred: str, gt: str) -> float:
        """
        计算字符级准确率
        
        Args:
            pred: 预测文本
            gt: 真实标准文本
            
        Returns:
            字符级准确率
        """
        if not gt:
            return 1.0 if not pred else 0.0
        
        if not pred:
            return 0.0
        
        # 计算正确字符数
        correct_chars = 0
        min_length = min(len(pred), len(gt))
        
        for i in range(min_length):
            if pred[i] == gt[i]:
                correct_chars += 1
        
        return correct_chars / len(gt)


def main():
    """测试函数"""
    ned = NED()
    
    # 测试用例
    test_cases = [
        ("完全相同的文本", "完全相同的文本"),
        ("有一些差异的文本", "有一些不同的文本"),
        ("", ""),
        ("非空文本", ""),
        ("", "非空文本"),
        ("Hello World", "hello world"),  # 大小写测试
        ("多个    空格", "多个 空格"),  # 空格标准化测试
    ]
    
    print("NED测试结果:")
    print("-" * 50)
    
    for pred, gt in test_cases:
        score = ned(pred, gt)
        detailed = ned.compute_detailed_metrics(pred, gt)
        
        print(f"预测: '{pred}'")
        print(f"真实: '{gt}'")
        print(f"NED分数: {score:.4f}")
        print(f"编辑距离: {detailed['edit_distance']}")
        print(f"字符准确率: {detailed['char_accuracy']:.4f}")
        print("-" * 30)


if __name__ == "__main__":
    main()
