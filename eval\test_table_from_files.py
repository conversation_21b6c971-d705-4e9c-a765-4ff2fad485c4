#!/usr/bin/env python3
"""
从文件中读取表格并比较TEDS分数的脚本

此脚本用于从两个目录（truth和output）中读取HTML文件并比较表格的TEDS分数。

使用方法：
python eval/test_table_from_files.py [--truth-dir PATH] [--output-dir PATH] [--use-demo-files]

参数说明：
--truth-dir: 真实标准HTML文件所在目录（默认：truth）
--output-dir: 预测HTML文件所在目录（默认：output）
--use-demo-files: 可选标志，如果提供则使用eval目录下的演示文件进行测试

示例：
1. 使用默认目录：
   python eval/test_table_from_files.py

2. 指定自定义目录：
   python eval/test_table_from_files.py --truth-dir /path/to/truth --output-dir /path/to/output

3. 使用eval目录下的演示文件：
   python eval/test_table_from_files.py --use-demo-files

评估结果解释：
- 优秀 (TEDS分数 >= 0.9): 表格高度相似，结构和内容几乎完全匹配
- 良好 (0.7 <= TEDS分数 < 0.9): 表格相似度较高，可能存在少量差异
- 一般 (0.5 <= TEDS分数 < 0.7): 表格有一定相似度，但存在明显差异
- 较差 (TEDS分数 < 0.5): 表格相似度低，结构和/或内容差异较大
"""

import argparse
import os
import sys
from pathlib import Path
from bs4 import BeautifulSoup

# 添加当前目录到Python路径，以便导入eval模块
sys.path.insert(0, str(Path(__file__).parent))

from table_recognition_metric import TEDS


def extract_tables_from_html(html_content):
    """
    从HTML内容中提取所有表格
    
    Args:
        html_content (str): HTML内容
        
    Returns:
        list: 表格HTML列表
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    tables = soup.find_all('table')
    
    # 将每个表格转换为HTML字符串
    table_htmls = []
    for table in tables:
        table_htmls.append(str(table))
    
    return table_htmls


def read_html_file(file_path):
    """
    读取HTML文件内容
    
    Args:
        file_path (str): HTML文件路径
        
    Returns:
        str: HTML内容
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
        return None


def find_html_files(directory):
    """
    查找目录中的所有HTML文件
    
    Args:
        directory (str): 目录路径
        
    Returns:
        list: HTML文件路径列表
    """
    html_files = []
    
    if not os.path.exists(directory):
        print(f"目录 {directory} 不存在")
        return html_files
    
    for file in os.listdir(directory):
        if file.endswith('.html') or file.endswith('.htm'):
            html_files.append(os.path.join(directory, file))
    
    return html_files


def test_tables_from_files(truth_dir, output_dir):
    """
    从文件中读取表格并测试TEDS分数
    
    Args:
        truth_dir (str): 真实标准HTML文件所在目录
        output_dir (str): 预测HTML文件所在目录
    """
    # 查找HTML文件
    truth_files = find_html_files(truth_dir)
    output_files = find_html_files(output_dir)
    
    if not truth_files:
        print(f"在目录 {truth_dir} 中未找到HTML文件")
        return
    
    if not output_files:
        print(f"在目录 {output_dir} 中未找到HTML文件")
        return
    
    # 特殊处理eval目录下的演示文件
    if truth_dir == output_dir:
        # 找出所有truth_开头的文件
        truth_files_filtered = [f for f in truth_files if os.path.splitext(os.path.basename(f))[0].startswith("truth_")]
        # 找出所有output_开头的文件
        output_files_filtered = [f for f in output_files if os.path.splitext(os.path.basename(f))[0].startswith("output_")]
        
        # 将truth文件与对应的output文件进行匹配
        matched_pairs = []
        for truth_file in truth_files_filtered:
            # 提取truth文件名中的标识符（例如truth_1.html中的"1"）
            truth_id = os.path.splitext(os.path.basename(truth_file))[0][6:]  # 去掉"truth_"前缀
            
            # 查找对应的output文件
            for output_file in output_files_filtered:
                output_id = os.path.splitext(os.path.basename(output_file))[0][7:]  # 去掉"output_"前缀
                
                if truth_id == output_id:
                    matched_pairs.append((truth_file, output_file))
                    break
    else:
        # 匹配文件
        matched_pairs = []
        # 提取文件名（不包含路径和扩展名）
        truth_names = [os.path.splitext(os.path.basename(f))[0] for f in truth_files]
        output_names = [os.path.splitext(os.path.basename(f))[0] for f in output_files]
        
        # 找到匹配的文件名
        for i, truth_name in enumerate(truth_names):
            if truth_name in output_names:
                j = output_names.index(truth_name)
                matched_pairs.append((truth_files[i], output_files[j]))
    
    if not matched_pairs:
        print("未找到匹配的文件对")
        return
    
    print(f"找到 {len(matched_pairs)} 对匹配的文件")
    print("=" * 80)
    
    # 创建TEDS评估器
    teds = TEDS()
    
    # 对每对文件进行比较
    for truth_file, output_file in matched_pairs:
        print(f"\n比较文件:")
        print(f"  真实标准: {os.path.basename(truth_file)}")
        print(f"  预测结果: {os.path.basename(output_file)}")
        print("-" * 60)
        
        # 读取文件内容
        truth_content = read_html_file(truth_file)
        output_content = read_html_file(output_file)
        
        if not truth_content or not output_content:
            print("读取文件内容失败，跳过此对文件")
            continue
        
        # 提取表格
        truth_tables = extract_tables_from_html(truth_content)
        output_tables = extract_tables_from_html(output_content)
        
        if not truth_tables:
            print(f"在真实标准文件 {os.path.basename(truth_file)} 中未找到表格")
            continue
        
        if not output_tables:
            print(f"在预测结果文件 {os.path.basename(output_file)} 中未找到表格")
            continue
        
        print(f"真实标准文件包含 {len(truth_tables)} 个表格")
        print(f"预测结果文件包含 {len(output_tables)} 个表格")
        
        # 比较表格数量
        if len(truth_tables) != len(output_tables):
            print(f"警告: 表格数量不匹配 (真实标准: {len(truth_tables)}, 预测结果: {len(output_tables)})")
        
        # 比较每个表格
        min_tables = min(len(truth_tables), len(output_tables))
        for i in range(min_tables):
            print(f"\n表格 {i+1}:")
            
            # 包装HTML
            truth_html = f"<html><body>{truth_tables[i]}</body></html>"
            output_html = f"<html><body>{output_tables[i]}</body></html>"
            
            # 计算TEDS分数
            score = teds(truth_html, output_html)
            
            # 输出结果
            print(f"  TEDS分数: {score:.4f}")
            
            # 解释分数
            if score >= 0.9:
                print("  评估结果: 优秀")
            elif score >= 0.7:
                print("  评估结果: 良好")
            elif score >= 0.5:
                print("  评估结果: 一般")
            else:
                print("  评估结果: 较差")


def main():
    """
    主函数，处理命令行参数并执行表格评估
    """
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="从文件中读取表格并比较TEDS分数")
    parser.add_argument("--truth-dir", type=str, default="truth", help="真实标准HTML文件所在目录（默认：truth）")
    parser.add_argument("--output-dir", type=str, default="output", help="预测HTML文件所在目录（默认：output）")
    parser.add_argument("--use-demo-files", action="store_true", help="使用eval目录下的演示文件进行测试")
    
    # 解析参数
    args = parser.parse_args()
    
    # 如果指定使用演示文件，则使用eval目录下的文件
    if args.use_demo_files:
        eval_dir = os.path.abspath(os.path.dirname(__file__))
        truth_dir = eval_dir
        output_dir = eval_dir
        print("使用eval目录下的演示文件进行测试")
    else:
        # 获取绝对路径
        truth_dir = os.path.abspath(args.truth_dir)
        output_dir = os.path.abspath(args.output_dir)
    
    print("表格TEDS评估工具 - 从文件读取表格")
    print("=" * 80)
    print(f"真实标准目录: {truth_dir}")
    print(f"预测结果目录: {output_dir}")
    print("=" * 80)
    
    # 如果output目录不存在，使用当前目录下的HTML文件作为示例
    if not os.path.exists(output_dir):
        print(f"警告: 目录 {output_dir} 不存在，使用当前目录下的HTML文件作为示例")
        output_dir = os.path.abspath(".")
    
    # 执行表格测试
    test_tables_from_files(truth_dir, output_dir)


if __name__ == "__main__":
    main()