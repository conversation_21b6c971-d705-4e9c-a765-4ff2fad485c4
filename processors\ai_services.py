"""
AI服务模块 - 阿里云LLM API调用
"""

import time
import json
from typing import Dict, Any, Optional
from openai import OpenAI
from config import (
    LLM_API_KEY,
    LLM_BASE_URL,
    LLM_MODEL,
    LLM_TEMPERATURE,
    LLM_MAX_TOKENS,
    LLM_TIMEOUT,
    MAX_RETRIES,
    RETRY_DELAY,
    DEFAULT_CONTEXT_LINES,
)
from utils.logger import get_logger
from utils.html_utils import get_last_rows, get_first_rows
from utils.error_handler import error_handler, retry_on_failure, APIError
from utils.prompt_loader import prompt_loader
from utils.llm_logger import llm_call_logger

logger = get_logger(__name__)


class LLMService:
    """LLM服务类"""

    def __init__(self):
        """初始化LLM客户端"""
        self.client = OpenAI(
            api_key=LLM_API_KEY,
            base_url=LLM_BASE_URL,
        )
        logger.info("LLM服务初始化完成")

    def _call_llm(self, prompt: str, system_prompt: str = None) -> str:
        """
        调用LLM API

        Args:
            prompt: 用户提示
            system_prompt: 系统提示

        Returns:
            LLM响应
        """
        messages = []

        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})

        messages.append({"role": "user", "content": prompt})

        for attempt in range(MAX_RETRIES):
            try:
                logger.debug(f"LLM调用尝试 {attempt + 1}/{MAX_RETRIES}")

                response = self.client.chat.completions.create(
                    model=LLM_MODEL,
                    messages=messages,
                    temperature=LLM_TEMPERATURE,
                    max_tokens=LLM_MAX_TOKENS,
                    timeout=LLM_TIMEOUT,
                )

                result = response.choices[0].message.content.strip()
                logger.debug(f"LLM响应: {result[:100]}...")

                return result

            except Exception as e:
                logger.warning(f"LLM调用失败 (尝试 {attempt + 1}): {e}")

                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_DELAY * (2**attempt))  # 指数退避
                else:
                    error_handler.record_error(
                        "API_ERROR",
                        f"LLM调用失败: {e}",
                        {"attempt": attempt + 1, "max_retries": MAX_RETRIES},
                    )
                    raise APIError(f"LLM调用失败: {e}")

    def should_merge_first_row(
        self, prev_table: Dict[str, Any], next_table: Dict[str, Any]
    ) -> bool:
        """
        判断是否需要合并表格的首行

        Args:
            prev_table: 上一页的表格数据
            next_table: 下一页的表格数据

        Returns:
            是否需要合并首行
        """
        try:
            # 提取关键信息
            prev_last_rows = get_last_rows(
                prev_table["table_body"], DEFAULT_CONTEXT_LINES
            )
            next_first_rows = get_first_rows(
                next_table["table_body"], DEFAULT_CONTEXT_LINES
            )

            # 从提示词加载器获取提示词
            table_prompts = prompt_loader.get_table_merge_prompts()
            system_prompt = table_prompts.get("system", "")
            # 直接使用HTML格式的行数据
            prompt = prompt_loader.format_table_merge_prompt(
                prev_last_rows, next_first_rows
            )

            response = self._call_llm(prompt, system_prompt)
            result = response.upper().strip() == "YES"

            # 记录LLM调用
            llm_call_logger.log_call(
                call_type="table_merge",
                system_prompt=system_prompt,
                user_prompt=prompt,
                response=response,
                result=result,
                context={
                    "prev_table_page": prev_table.get("page_idx"),
                    "next_table_page": next_table.get("page_idx"),
                    "prev_last_rows_html": prev_last_rows,  # 原始HTML行数据
                    "next_first_rows_html": next_first_rows,  # 原始HTML行数据
                },
            )

            logger.info(f"首行合并判断结果: {result}")
            return result

        except Exception as e:
            logger.error(f"首行合并判断失败: {e}")
            # 默认不合并，避免错误
            return False

    def fix_merged_tables(self, tables: list) -> str:
        """
        使用LLM直接修复多个表格

        Args:
            tables: 表格HTML字符串列表

        Returns:
            修复后的完整HTML表格字符串
        """
        try:
            if not tables:
                logger.warning("表格列表为空")
                return ""

            if len(tables) == 1:
                logger.info("只有一个表格，直接返回")
                return tables[0]

            logger.info(f"开始修复 {len(tables)} 个表格")

            # 从提示词加载器获取提示词
            table_fix_prompts = prompt_loader.get_table_fix_prompts()
            system_prompt = table_fix_prompts.get("system", "")
            user_prompt = prompt_loader.format_table_fix_prompt(tables)

            response = self._call_llm(user_prompt, system_prompt)

            # 记录LLM调用
            llm_call_logger.log_call(
                call_type="table_fix",
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                response=response,
                result=True,
                context={
                    "table_count": len(tables),
                    "input_tables": (
                        tables[:2] if len(tables) > 2 else tables
                    ),  # 只记录前两个表格避免日志过长
                },
            )

            logger.info("表格修复完成")
            return response.strip()

        except Exception as e:
            logger.error(f"表格修复失败: {e}")
            # 降级策略：简单拼接所有表格
            logger.info("使用降级策略：简单拼接表格")
            return self._fallback_merge_tables(tables)

    def _fallback_merge_tables(self, tables: list) -> str:
        """
        降级策略：简单合并表格

        Args:
            tables: 表格HTML字符串列表

        Returns:
            合并后的HTML字符串
        """
        try:
            from bs4 import BeautifulSoup

            if not tables:
                return ""

            if len(tables) == 1:
                return tables[0]

            # 解析第一个表格作为基础
            soup = BeautifulSoup(tables[0], "html.parser")
            base_table = soup.find("table")

            if not base_table:
                # 如果第一个表格解析失败，直接拼接所有表格
                return "\n".join(tables)

            # 将其他表格的行添加到基础表格中
            for table_html in tables[1:]:
                table_soup = BeautifulSoup(table_html, "html.parser")
                table = table_soup.find("table")

                if table:
                    # 添加所有行到基础表格
                    for tr in table.find_all("tr"):
                        base_table.append(tr)

            return str(base_table)

        except Exception as e:
            logger.error(f"降级合并失败: {e}")
            # 最后的降级策略：直接拼接
            return "\n".join(tables)

    def should_join_text(
        self, prev_text: Dict[str, Any], next_text: Dict[str, Any]
    ) -> bool:
        """
        判断是否需要拼接文本

        Args:
            prev_text: 上一页的文本数据
            next_text: 下一页的文本数据

        Returns:
            是否需要拼接文本
        """
        try:
            prev_content = prev_text["text"].strip()
            next_content = next_text["text"].strip()

            # 如果任一文本为空，不拼接
            if not prev_content or not next_content:
                return False

            # 从提示词加载器获取提示词
            text_prompts = prompt_loader.get_text_join_prompts()
            system_prompt = text_prompts.get("system", "")
            prompt = prompt_loader.format_text_join_prompt(prev_content, next_content)

            response = self._call_llm(prompt, system_prompt)
            result = response.upper().strip() == "YES"

            # 记录LLM调用
            llm_call_logger.log_call(
                call_type="text_join",
                system_prompt=system_prompt,
                user_prompt=prompt,
                response=response,
                result=result,
                context={
                    "prev_text_page": prev_text.get("page_idx"),
                    "next_text_page": next_text.get("page_idx"),
                    "prev_content": prev_content,
                    "next_content": next_content,
                },
            )

            logger.info(f"文本拼接判断结果: {result}")
            return result

        except Exception as e:
            logger.error(f"文本拼接判断失败: {e}")
            # 默认不拼接，避免错误
            return False


# 创建全局LLM服务实例
llm_service = LLMService()
