"""
HTML处理工具模块
"""
from typing import List, Dict, Any, Optional
from bs4 import BeautifulSoup
from utils.logger import get_logger

logger = get_logger(__name__)


def parse_html_table(html_table: str) -> List[List[Dict[str, Any]]]:
    """
    解析HTML表格为结构化数据
    
    Args:
        html_table: HTML表格字符串
        
    Returns:
        二维数组，每个单元格包含文本和属性信息
    """
    try:
        soup = BeautifulSoup(html_table, 'html.parser')
        table = soup.find('table')
        
        if not table:
            logger.warning("未找到table标签")
            return []
        
        rows = []
        for tr in table.find_all('tr'):
            row = []
            for cell in tr.find_all(['td', 'th']):
                cell_data = {
                    'text': cell.get_text(strip=True),
                    'colspan': int(cell.get('colspan', 1)),
                    'rowspan': int(cell.get('rowspan', 1)),
                    'tag': cell.name,
                    'attributes': dict(cell.attrs)
                }
                row.append(cell_data)
            
            if row:  # 只添加非空行
                rows.append(row)
        
        logger.debug(f"解析HTML表格成功，{len(rows)} 行")
        return rows
        
    except Exception as e:
        logger.error(f"HTML表格解析失败: {e}")
        return []


def extract_table_rows(html_table: str, start_row: int = 0, end_row: Optional[int] = None) -> str:
    """
    提取表格的指定行
    
    Args:
        html_table: HTML表格字符串
        start_row: 起始行（包含）
        end_row: 结束行（不包含），None表示到最后
        
    Returns:
        提取的行的HTML字符串
    """
    try:
        soup = BeautifulSoup(html_table, 'html.parser')
        table = soup.find('table')
        
        if not table:
            return ""
        
        rows = table.find_all('tr')
        
        if end_row is None:
            selected_rows = rows[start_row:]
        else:
            selected_rows = rows[start_row:end_row]
        
        # 构建新的表格HTML
        if selected_rows:
            new_table = soup.new_tag('table')
            for row in selected_rows:
                new_table.append(row)
            return str(new_table)
        
        return ""
        
    except Exception as e:
        logger.error(f"提取表格行失败: {e}")
        return ""


def get_last_rows(html_table: str, num_rows: int = 2) -> str:
    """
    获取表格的最后几行
    
    Args:
        html_table: HTML表格字符串
        num_rows: 要获取的行数
        
    Returns:
        最后几行的HTML字符串
    """
    try:
        soup = BeautifulSoup(html_table, 'html.parser')
        table = soup.find('table')
        
        if not table:
            return ""
        
        rows = table.find_all('tr')
        last_rows = rows[-num_rows:] if len(rows) >= num_rows else rows
        
        if last_rows:
            new_table = soup.new_tag('table')
            for row in last_rows:
                new_table.append(row)
            return str(new_table)
        
        return ""
        
    except Exception as e:
        logger.error(f"获取表格最后几行失败: {e}")
        return ""


def get_first_rows(html_table: str, num_rows: int = 2) -> str:
    """
    获取表格的前几行
    
    Args:
        html_table: HTML表格字符串
        num_rows: 要获取的行数
        
    Returns:
        前几行的HTML字符串
    """
    try:
        soup = BeautifulSoup(html_table, 'html.parser')
        table = soup.find('table')
        
        if not table:
            return ""
        
        rows = table.find_all('tr')
        first_rows = rows[:num_rows] if len(rows) >= num_rows else rows
        
        if first_rows:
            new_table = soup.new_tag('table')
            for row in first_rows:
                new_table.append(row)
            return str(new_table)
        
        return ""
        
    except Exception as e:
        logger.error(f"获取表格前几行失败: {e}")
        return ""



def clean_html_table(html_table: str) -> str:
    """
    清理HTML表格，移除不必要的属性和空白
    
    Args:
        html_table: HTML表格字符串
        
    Returns:
        清理后的HTML字符串
    """
    try:
        soup = BeautifulSoup(html_table, 'html.parser')
        
        # 移除不必要的属性（保留colspan, rowspan）
        for tag in soup.find_all():
            if tag.name in ['table', 'tr', 'td', 'th']:
                attrs_to_keep = []
                if 'colspan' in tag.attrs:
                    attrs_to_keep.append('colspan')
                if 'rowspan' in tag.attrs:
                    attrs_to_keep.append('rowspan')
                
                # 清除所有属性，然后重新添加需要保留的
                old_attrs = dict(tag.attrs)
                tag.attrs.clear()
                for attr in attrs_to_keep:
                    if attr in old_attrs:
                        tag.attrs[attr] = old_attrs[attr]
        
        return str(soup)
        
    except Exception as e:
        logger.error(f"清理HTML表格失败: {e}")
        return html_table


def html_table_to_text(html_table: str) -> str:
    """
    将HTML表格转换为纯文本（用于LLM分析）
    
    Args:
        html_table: HTML表格字符串
        
    Returns:
        表格的文本表示
    """
    try:
        soup = BeautifulSoup(html_table, 'html.parser')
        table = soup.find('table')
        
        if not table:
            return ""
        
        text_lines = []
        for tr in table.find_all('tr'):
            row_texts = []
            for cell in tr.find_all(['td', 'th']):
                cell_text = cell.get_text(strip=True)
                colspan = int(cell.get('colspan', 1))
                
                # 处理colspan
                if colspan > 1:
                    cell_text = f"{cell_text} (跨{colspan}列)"
                
                row_texts.append(cell_text)
            
            text_lines.append(" | ".join(row_texts))
        
        return "\n".join(text_lines)
        
    except Exception as e:
        logger.error(f"HTML表格转文本失败: {e}")
        return ""
