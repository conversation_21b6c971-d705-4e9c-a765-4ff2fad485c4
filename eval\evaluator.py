"""
主评估器 - 协调文本和表格的评估过程
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

from .content_extractor import ContentExtractor
from .text_metric import NED
from .table_recognition_metric import TEDS
from .table_matcher import TableMatcher


class Evaluator:
    """主评估器，协调文本和表格评估"""

    def __init__(
        self,
        text_normalize_whitespace: bool = True,
        text_ignore_case: bool = False,
        table_structure_only: bool = False,
        table_ignore_nodes: Optional[str] = None,
        enable_smart_table_matching: bool = True,
        table_match_threshold: float = 0.6,
    ):
        """
        初始化评估器

        Args:
            text_normalize_whitespace: 文本评估时是否标准化空白字符
            text_ignore_case: 文本评估时是否忽略大小写
            table_structure_only: 表格评估时是否只考虑结构
            table_ignore_nodes: 表格评估时要忽略的节点
            enable_smart_table_matching: 是否启用智能表格匹配
            table_match_threshold: 表格匹配相似度阈值
        """
        self.content_extractor = ContentExtractor()
        self.text_metric = NED(
            normalize_whitespace=text_normalize_whitespace, ignore_case=text_ignore_case
        )
        self.table_metric = TEDS(
            structure_only=table_structure_only, ignore_nodes=table_ignore_nodes
        )
        self.enable_smart_table_matching = enable_smart_table_matching
        self.table_matcher = (
            TableMatcher(similarity_threshold=table_match_threshold)
            if enable_smart_table_matching
            else None
        )

    def evaluate_file_pair(
        self, truth_file: str | Path, output_file: str | Path
    ) -> Dict[str, Any]:
        """
        评估单个文件对

        Args:
            truth_file: 真实标准文件路径
            output_file: 输出文件路径

        Returns:
            评估结果字典
        """
        try:
            # 提取内容
            comparison_data = self.content_extractor.extract_comparison_data(
                truth_file, output_file
            )

            if (
                "error" in comparison_data["truth"]
                or "error" in comparison_data["output"]
            ):
                return {
                    "error": "Failed to extract content",
                    "truth_error": comparison_data["truth"].get("error"),
                    "output_error": comparison_data["output"].get("error"),
                    "files": comparison_data["files"],
                }

            # 评估文本
            text_result = self._evaluate_text(
                comparison_data["truth"]["text_content"],
                comparison_data["output"]["text_content"],
            )

            # 评估表格
            table_result = self._evaluate_tables(
                comparison_data["truth"]["tables"], comparison_data["output"]["tables"]
            )

            # 计算综合分数
            overall_score = self._compute_overall_score(text_result, table_result)

            return {
                "files": comparison_data["files"],
                "text_evaluation": text_result,
                "table_evaluation": table_result,
                "overall_score": overall_score,
                "content_stats": {
                    "truth": {
                        "text_length": comparison_data["truth"]["text_length"],
                        "table_count": comparison_data["truth"]["table_count"],
                    },
                    "output": {
                        "text_length": comparison_data["output"]["text_length"],
                        "table_count": comparison_data["output"]["table_count"],
                    },
                },
            }

        except Exception as e:
            return {
                "error": f"Evaluation failed: {str(e)}",
                "files": {"truth": str(truth_file), "output": str(output_file)},
            }

    def _evaluate_text(self, truth_text: str, output_text: str) -> Dict[str, Any]:
        """
        评估文本内容

        Args:
            truth_text: 真实标准文本
            output_text: 输出文本

        Returns:
            文本评估结果
        """
        try:
            # 计算详细指标
            detailed_metrics = self.text_metric.compute_detailed_metrics(
                output_text, truth_text
            )

            return {
                "ned_score": detailed_metrics["ned_score"],
                "edit_distance": detailed_metrics["edit_distance"],
                "char_accuracy": detailed_metrics["char_accuracy"],
                "length_stats": {
                    "truth_length": detailed_metrics["gt_length"],
                    "output_length": detailed_metrics["pred_length"],
                    "length_ratio": detailed_metrics["length_ratio"],
                },
                "preprocessing_config": detailed_metrics["preprocessing"],
            }

        except Exception as e:
            return {"error": f"Text evaluation failed: {str(e)}", "ned_score": 0.0}

    def _evaluate_tables(
        self, truth_tables: List[str], output_tables: List[str]
    ) -> Dict[str, Any]:
        """
        评估表格内容，支持智能匹配

        Args:
            truth_tables: 真实标准表格列表
            output_tables: 输出表格列表

        Returns:
            表格评估结果
        """
        try:
            if not truth_tables and not output_tables:
                return {
                    "table_count": 0,
                    "avg_teds_score": 1.0,
                    "individual_scores": [],
                    "missing_tables": 0,
                    "extra_tables": 0,
                    "matched_pairs": 0,
                    "unmatched_truth_tables": 0,
                    "unmatched_output_tables": 0,
                }

            if self.enable_smart_table_matching and self.table_matcher:
                return self._evaluate_tables_with_smart_matching(
                    truth_tables, output_tables
                )
            else:
                return self._evaluate_tables_sequential(truth_tables, output_tables)

        except Exception as e:
            return {
                "error": f"Table evaluation failed: {str(e)}",
                "avg_teds_score": 0.0,
                "table_count": len(truth_tables) if truth_tables else 0,
            }

    def _evaluate_tables_with_smart_matching(
        self, truth_tables: List[str], output_tables: List[str]
    ) -> Dict[str, Any]:
        """
        使用智能匹配评估表格

        Args:
            truth_tables: 真实标准表格列表
            output_tables: 输出表格列表

        Returns:
            表格评估结果
        """
        # 使用表格匹配器找到最佳匹配
        mapping = self.table_matcher.match_tables(truth_tables, output_tables)

        individual_scores = []
        matched_pairs = 0

        # 评估匹配的表格对
        for truth_idx, output_idx in mapping.items():
            truth_table = truth_tables[truth_idx]

            if output_idx is not None:
                # 有匹配的表格
                output_table = output_tables[output_idx]
                truth_html = self._wrap_table_html(truth_table)
                output_html = self._wrap_table_html(output_table)
                teds_score = self.table_metric(output_html, truth_html)
                matched_pairs += 1
            else:
                # 未匹配的真实表格，给予0分
                teds_score = 0.0

            individual_scores.append(
                {
                    "truth_index": truth_idx,
                    "output_index": output_idx,
                    "teds_score": teds_score,
                    "matched": output_idx is not None,
                }
            )

        # 计算统计信息
        valid_scores = [score["teds_score"] for score in individual_scores]
        avg_teds_score = sum(valid_scores) / len(valid_scores) if valid_scores else 0.0

        unmatched_truth_tables = len([s for s in individual_scores if not s["matched"]])
        unmatched_output_tables = len(output_tables) - matched_pairs

        return {
            "table_count": len(truth_tables),
            "output_table_count": len(output_tables),
            "avg_teds_score": avg_teds_score,
            "individual_scores": individual_scores,
            "matched_pairs": matched_pairs,
            "unmatched_truth_tables": unmatched_truth_tables,
            "unmatched_output_tables": unmatched_output_tables,
            "matching_method": "smart_matching",
        }

    def _evaluate_tables_sequential(
        self, truth_tables: List[str], output_tables: List[str]
    ) -> Dict[str, Any]:
        """
        使用顺序匹配评估表格（原有逻辑）

        Args:
            truth_tables: 真实标准表格列表
            output_tables: 输出表格列表

        Returns:
            表格评估结果
        """
        individual_scores = []
        max_tables = max(len(truth_tables), len(output_tables))

        for i in range(max_tables):
            truth_table = truth_tables[i] if i < len(truth_tables) else ""
            output_table = output_tables[i] if i < len(output_tables) else ""

            if truth_table or output_table:
                # 为TEDS包装HTML结构
                truth_html = self._wrap_table_html(truth_table) if truth_table else ""
                output_html = (
                    self._wrap_table_html(output_table) if output_table else ""
                )
                teds_score = self.table_metric(output_html, truth_html)
                individual_scores.append(
                    {
                        "table_index": i,
                        "teds_score": teds_score,
                        "truth_present": bool(truth_table),
                        "output_present": bool(output_table),
                    }
                )

        # 计算统计信息
        valid_scores = [
            score["teds_score"] for score in individual_scores if score["truth_present"]
        ]
        avg_teds_score = sum(valid_scores) / len(valid_scores) if valid_scores else 0.0

        missing_tables = (
            len(truth_tables) - len(output_tables)
            if len(truth_tables) > len(output_tables)
            else 0
        )
        extra_tables = (
            len(output_tables) - len(truth_tables)
            if len(output_tables) > len(truth_tables)
            else 0
        )

        return {
            "table_count": len(truth_tables),
            "output_table_count": len(output_tables),
            "avg_teds_score": avg_teds_score,
            "individual_scores": individual_scores,
            "missing_tables": missing_tables,
            "extra_tables": extra_tables,
            "matching_method": "sequential",
        }

    def _wrap_table_html(self, table_html: str) -> str:
        """
        为TEDS评估包装表格HTML

        Args:
            table_html: 原始表格HTML

        Returns:
            包装后的HTML
        """
        if not table_html:
            return ""

        # TEDS期望表格被包装在body标签中
        return f"<html><body>{table_html}</body></html>"

    def _compute_overall_score(
        self, text_result: Dict[str, Any], table_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        计算综合评估分数

        Args:
            text_result: 文本评估结果
            table_result: 表格评估结果

        Returns:
            综合评估分数
        """
        text_score = text_result.get("ned_score", 0.0)
        table_score = table_result.get("avg_teds_score", 0.0)

        # 根据内容比例计算权重
        text_weight = 0.5  # 默认权重
        table_weight = 0.5

        # 如果只有文本或只有表格，调整权重
        has_text = text_result.get("length_stats", {}).get("truth_length", 0) > 0
        has_tables = table_result.get("table_count", 0) > 0

        if has_text and not has_tables:
            text_weight = 1.0
            table_weight = 0.0
        elif has_tables and not has_text:
            text_weight = 0.0
            table_weight = 1.0

        # 计算加权平均分数
        weighted_score = text_score * text_weight + table_score * table_weight

        return {
            "weighted_score": weighted_score,
            "text_score": text_score,
            "table_score": table_score,
            "weights": {"text_weight": text_weight, "table_weight": table_weight},
            "content_type": {"has_text": has_text, "has_tables": has_tables},
        }

    def evaluate_batch(self, file_pairs: List[Tuple[Path, Path]]) -> Dict[str, Any]:
        """
        批量评估文件对

        Args:
            file_pairs: 文件对列表

        Returns:
            批量评估结果
        """
        results = []

        for truth_file, output_file in file_pairs:
            result = self.evaluate_file_pair(truth_file, output_file)
            results.append(result)

        # 计算汇总统计
        summary = self._compute_batch_summary(results)

        return {
            "total_files": len(file_pairs),
            "individual_results": results,
            "summary": summary,
        }

    def _compute_batch_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        计算批量评估的汇总统计

        Args:
            results: 个别评估结果列表

        Returns:
            汇总统计
        """
        valid_results = [r for r in results if "error" not in r]

        if not valid_results:
            return {"error": "No valid results to summarize"}

        # 收集分数
        text_scores = [
            r["text_evaluation"]["ned_score"]
            for r in valid_results
            if "ned_score" in r["text_evaluation"]
        ]
        table_scores = [
            r["table_evaluation"]["avg_teds_score"]
            for r in valid_results
            if "avg_teds_score" in r["table_evaluation"]
        ]
        overall_scores = [r["overall_score"]["weighted_score"] for r in valid_results]

        return {
            "valid_files": len(valid_results),
            "failed_files": len(results) - len(valid_results),
            "text_metrics": {
                "avg_ned_score": (
                    sum(text_scores) / len(text_scores) if text_scores else 0.0
                ),
                "min_ned_score": min(text_scores) if text_scores else 0.0,
                "max_ned_score": max(text_scores) if text_scores else 0.0,
            },
            "table_metrics": {
                "avg_teds_score": (
                    sum(table_scores) / len(table_scores) if table_scores else 0.0
                ),
                "min_teds_score": min(table_scores) if table_scores else 0.0,
                "max_teds_score": max(table_scores) if table_scores else 0.0,
            },
            "overall_metrics": {
                "avg_weighted_score": (
                    sum(overall_scores) / len(overall_scores) if overall_scores else 0.0
                ),
                "min_weighted_score": min(overall_scores) if overall_scores else 0.0,
                "max_weighted_score": max(overall_scores) if overall_scores else 0.0,
            },
        }


def main():
    """测试函数"""
    evaluator = Evaluator()

    # 测试单个文件对评估
    truth_file = Path("truth/data.md")
    output_file = Path("output/data.md")

    if truth_file.exists() and output_file.exists():
        result = evaluator.evaluate_file_pair(truth_file, output_file)
        print("评估结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print("测试文件不存在")


if __name__ == "__main__":
    main()
