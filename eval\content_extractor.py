"""
内容提取器 - 从markdown文件中分离文本和表格内容
"""

import re
from typing import List, Tuple, Dict, Any
from pathlib import Path


class ContentExtractor:
    """从markdown文件中提取文本和表格内容"""

    def __init__(self):
        # 匹配HTML表格的正则表达式
        self.table_pattern = re.compile(
            r"<table[^>]*>.*?</table>", re.DOTALL | re.IGNORECASE
        )

    def extract_from_file(self, file_path: str | Path) -> Dict[str, Any]:
        """
        从文件中提取内容

        Args:
            file_path: markdown文件路径

        Returns:
            包含文本和表格内容的字典
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            return self.extract_from_content(content)

        except Exception as e:
            return {
                "error": f"读取文件失败: {str(e)}",
                "text_content": "",
                "tables": [],
                "table_count": 0,
            }

    def extract_from_content(self, content: str) -> Dict[str, Any]:
        """
        从内容字符串中提取文本和表格

        Args:
            content: markdown内容字符串

        Returns:
            包含文本和表格内容的字典
        """
        # 提取所有表格
        tables = self.table_pattern.findall(content)

        # 移除表格，获取纯文本内容
        text_content = self.table_pattern.sub("", content)

        # 清理文本内容
        text_content = self._clean_text(text_content)

        return {
            "text_content": text_content,
            "tables": tables,
            "table_count": len(tables),
            "original_length": len(content),
            "text_length": len(text_content),
        }

    def _clean_text(self, text: str) -> str:
        """
        清理文本内容，移除markdown格式符号

        Args:
            text: 原始文本

        Returns:
            清理后的文本
        """
        # 首先移除markdown格式符号
        text = self._remove_markdown_syntax(text)

        # 移除多余的空行
        text = re.sub(r"\n\s*\n\s*\n", "\n\n", text)

        # 移除行首行尾的空格
        lines = [line.strip() for line in text.split("\n")]

        # 移除空行（但保留段落分隔）
        cleaned_lines = []
        prev_empty = False

        for line in lines:
            if line:
                cleaned_lines.append(line)
                prev_empty = False
            elif not prev_empty:
                cleaned_lines.append("")
                prev_empty = True

        return "\n".join(cleaned_lines).strip()

    def _remove_markdown_syntax(self, text: str) -> str:
        """
        移除markdown格式符号

        Args:
            text: 包含markdown语法的文本

        Returns:
            移除markdown语法后的纯文本
        """
        # 移除代码块（三个反引号）
        text = re.sub(r"```[\s\S]*?```", "", text)

        # 移除行内代码（单个反引号）
        text = re.sub(r"`([^`]+)`", r"\1", text)

        # 移除标题符号（# ## ### 等）
        text = re.sub(r"^#{1,6}\s+", "", text, flags=re.MULTILINE)

        # 移除粗体和斜体
        # **粗体** 或 __粗体__
        text = re.sub(r"\*\*([^*]+)\*\*", r"\1", text)
        text = re.sub(r"__([^_]+)__", r"\1", text)

        # *斜体* 或 _斜体_
        text = re.sub(r"\*([^*]+)\*", r"\1", text)
        text = re.sub(r"_([^_]+)_", r"\1", text)

        # 移除链接 [文本](链接)
        text = re.sub(r"\[([^\]]+)\]\([^)]+\)", r"\1", text)

        # 移除图片 ![alt](src)
        text = re.sub(r"!\[([^\]]*)\]\([^)]+\)", r"\1", text)

        # 移除无序列表符号（- * +）
        text = re.sub(r"^[\s]*[-*+]\s+", "", text, flags=re.MULTILINE)

        # 移除有序列表符号（1. 2. 等）
        text = re.sub(r"^\s*\d+\.\s+", "", text, flags=re.MULTILINE)

        # 移除引用符号（>）
        text = re.sub(r"^>\s*", "", text, flags=re.MULTILINE)

        # 移除分割线（--- *** ___）
        text = re.sub(r"^[-*_]{3,}\s*$", "", text, flags=re.MULTILINE)

        # 移除表格分隔符行（如 ------ -----）
        text = re.sub(r"^[-\s]+$", "", text, flags=re.MULTILINE)

        # 移除表格分隔符（|）
        text = re.sub(r"\|", " ", text)

        # 移除删除线（~~文本~~）
        text = re.sub(r"~~([^~]+)~~", r"\1", text)

        # 移除高亮（==文本==）
        text = re.sub(r"==([^=]+)==", r"\1", text)

        return text

    def get_table_html_list(self, content: str) -> List[str]:
        """
        获取所有表格的HTML列表

        Args:
            content: markdown内容

        Returns:
            表格HTML字符串列表
        """
        return self.table_pattern.findall(content)

    def get_text_without_tables(self, content: str) -> str:
        """
        获取移除表格后的纯文本内容

        Args:
            content: markdown内容

        Returns:
            纯文本内容
        """
        text_content = self.table_pattern.sub("", content)
        return self._clean_text(text_content)

    def extract_comparison_data(
        self, truth_file: str | Path, output_file: str | Path
    ) -> Dict[str, Any]:
        """
        提取两个文件的对比数据

        Args:
            truth_file: 真实标准文件路径
            output_file: 输出文件路径

        Returns:
            包含两个文件提取内容的对比数据
        """
        truth_data = self.extract_from_file(truth_file)
        output_data = self.extract_from_file(output_file)

        return {
            "truth": truth_data,
            "output": output_data,
            "files": {"truth": str(truth_file), "output": str(output_file)},
        }


def main():
    """测试函数"""
    extractor = ContentExtractor()

    # 测试提取功能
    test_content = """
# 测试文档

这是一段测试文本。

<table><tr><td>列1</td><td>列2</td></tr><tr><td>数据1</td><td>数据2</td></tr></table>

这是另一段文本。

<table><tr><td>表格2</td></tr></table>

结束文本。
"""

    result = extractor.extract_from_content(test_content)
    print("提取结果:")
    print(f"文本内容: {repr(result['text_content'])}")
    print(f"表格数量: {result['table_count']}")
    print(f"表格内容: {result['tables']}")


if __name__ == "__main__":
    main()
