"""
文件操作工具模块
"""
import json
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
from utils.logger import get_logger

logger = get_logger(__name__)


def load_json(file_path: str | Path) -> List[Dict[str, Any]]:
    """
    安全加载JSON文件
    
    Args:
        file_path: JSON文件路径
        
    Returns:
        解析后的JSON数据
        
    Raises:
        FileNotFoundError: 文件不存在
        json.JSONDecodeError: JSON格式错误
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    logger.info(f"加载JSON文件: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"成功加载JSON文件，包含 {len(data)} 个元素")
        return data
        
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误: {e}")
        raise
    except Exception as e:
        logger.error(f"文件读取错误: {e}")
        raise


def save_markdown(content: str, file_path: str | Path) -> None:
    """
    保存Markdown文件
    
    Args:
        content: Markdown内容
        file_path: 输出文件路径
    """
    file_path = Path(file_path)
    file_path.parent.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"保存Markdown文件: {file_path}")
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"成功保存Markdown文件，大小: {len(content)} 字符")
        
    except Exception as e:
        logger.error(f"文件保存错误: {e}")
        raise


def find_content_list_files(directory: str | Path) -> List[Path]:
    """
    查找目录下的content_list.json文件
    
    Args:
        directory: 搜索目录
        
    Returns:
        找到的文件路径列表
    """
    directory = Path(directory)
    
    if not directory.exists():
        logger.warning(f"目录不存在: {directory}")
        return []
    
    pattern = "*_content_list.json"
    files = list(directory.glob(pattern))
    
    logger.info(f"在 {directory} 中找到 {len(files)} 个content_list文件")
    
    return sorted(files)


def generate_output_path(input_path: str | Path, output_dir: str | Path) -> Path:
    """
    根据输入文件路径生成输出文件路径
    
    Args:
        input_path: 输入文件路径
        output_dir: 输出目录
        
    Returns:
        输出文件路径
    """
    input_path = Path(input_path)
    output_dir = Path(output_dir)
    
    # 生成输出文件名：原文件名去掉_content_list.json，加上.md
    base_name = input_path.stem.replace('_content_list', '')
    output_file = output_dir / f"{base_name}.md"
    
    return output_file


def validate_content_list(data: List[Dict[str, Any]]) -> bool:
    """
    验证content_list数据格式
    
    Args:
        data: content_list数据
        
    Returns:
        是否有效
    """
    if not isinstance(data, list):
        logger.error("数据不是列表格式")
        return False
    
    if not data:
        logger.warning("数据为空")
        return True
    
    required_fields = {'type', 'page_idx'}
    
    for i, item in enumerate(data):
        if not isinstance(item, dict):
            logger.error(f"第 {i} 个元素不是字典格式")
            return False
        
        if not required_fields.issubset(item.keys()):
            missing = required_fields - item.keys()
            logger.error(f"第 {i} 个元素缺少必要字段: {missing}")
            return False
        
        if item['type'] not in ['text', 'table', 'image']:
            logger.error(f"第 {i} 个元素类型无效: {item['type']}")
            return False
        
        if item['type'] == 'text' and 'text' not in item:
            logger.error(f"第 {i} 个text元素缺少text字段")
            return False
        
        if item['type'] == 'table' and 'table_body' not in item:
            logger.error(f"第 {i} 个table元素缺少table_body字段")
            return False
            
        # 跳过image类型的验证，直接继续处理下一个元素
        if item['type'] == 'image':
            logger.info(f"第 {i} 个元素为image类型，跳过验证")
            continue
            
        # 跳过image类型的验证，直接继续处理下一个元素
        if item['type'] == 'image':
            logger.info(f"第 {i} 个元素为image类型，跳过验证")
            continue
    
    logger.info("数据格式验证通过")
    return True
