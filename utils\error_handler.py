"""
错误处理和降级策略模块
"""
import functools
import traceback
from typing import Any, Callable, Optional, Dict
from utils.logger import get_logger

logger = get_logger(__name__)


class ProcessingError(Exception):
    """处理过程中的自定义异常"""
    pass


class APIError(Exception):
    """API调用异常"""
    pass


class DataValidationError(Exception):
    """数据验证异常"""
    pass


def safe_execute(fallback_value: Any = None, log_error: bool = True):
    """
    安全执行装饰器，捕获异常并返回降级值
    
    Args:
        fallback_value: 异常时返回的降级值
        log_error: 是否记录错误日志
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if log_error:
                    logger.error(f"函数 {func.__name__} 执行失败: {e}")
                    logger.debug(f"详细错误信息: {traceback.format_exc()}")
                return fallback_value
        return wrapper
    return decorator


def retry_on_failure(max_retries: int = 3, delay: float = 1.0, 
                    exceptions: tuple = (Exception,)):
    """
    重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 重试间隔（秒）
        exceptions: 需要重试的异常类型
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import time
            
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}")
                        time.sleep(delay * (2 ** attempt))  # 指数退避
                    else:
                        logger.error(f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败")
                        break
            
            raise last_exception
        return wrapper
    return decorator


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        self.error_counts = {}
        self.error_details = []
    
    def record_error(self, error_type: str, error_message: str, 
                    context: Optional[Dict[str, Any]] = None):
        """
        记录错误信息
        
        Args:
            error_type: 错误类型
            error_message: 错误消息
            context: 错误上下文信息
        """
        # 统计错误次数
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # 记录详细错误信息
        error_detail = {
            'type': error_type,
            'message': error_message,
            'context': context or {},
            'timestamp': self._get_timestamp()
        }
        self.error_details.append(error_detail)
        
        logger.error(f"[{error_type}] {error_message}")
        if context:
            logger.debug(f"错误上下文: {context}")
    
    def get_error_summary(self) -> Dict[str, Any]:
        """
        获取错误摘要
        
        Returns:
            错误统计信息
        """
        return {
            'total_errors': len(self.error_details),
            'error_counts': self.error_counts.copy(),
            'recent_errors': self.error_details[-5:] if self.error_details else []
        }
    
    def has_critical_errors(self) -> bool:
        """
        检查是否有严重错误
        
        Returns:
            是否有严重错误
        """
        critical_types = ['API_ERROR', 'DATA_VALIDATION_ERROR', 'FILE_ERROR']
        return any(error_type in self.error_counts for error_type in critical_types)
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')


class FallbackStrategy:
    """降级策略"""
    
    @staticmethod
    def table_merge_fallback(prev_table: Dict[str, Any], 
                           next_table: Dict[str, Any]) -> Dict[str, Any]:
        """
        表格合并降级策略：简单拼接
        
        Args:
            prev_table: 上一页表格
            next_table: 下一页表格
            
        Returns:
            降级合并后的表格
        """
        logger.warning("使用表格合并降级策略")
        
        return {
            'type': 'table',
            'table_body': prev_table.get('table_body', '') + '\n\n' + next_table.get('table_body', ''),
            'table_caption': prev_table.get('table_caption', []) + next_table.get('table_caption', []),
            'table_footnote': prev_table.get('table_footnote', []) + next_table.get('table_footnote', []),
            'img_path': prev_table.get('img_path', ''),
            'page_idx': prev_table.get('page_idx', 0)
        }
    
    @staticmethod
    def text_join_fallback(prev_text: Dict[str, Any], 
                          next_text: Dict[str, Any]) -> Dict[str, Any]:
        """
        文本拼接降级策略：不拼接，保持原样
        
        Args:
            prev_text: 上一页文本
            next_text: 下一页文本
            
        Returns:
            原始文本（不拼接）
        """
        logger.warning("使用文本拼接降级策略：不拼接")
        return prev_text
    
    @staticmethod
    def markdown_generation_fallback(content_list: list) -> str:
        """
        Markdown生成降级策略：生成简单格式
        
        Args:
            content_list: 内容列表
            
        Returns:
            简单格式的Markdown
        """
        logger.warning("使用Markdown生成降级策略")
        
        parts = []
        for item in content_list:
            if item.get('type') == 'text':
                text = item.get('text', '').strip()
                if text:
                    parts.append(text)
            elif item.get('type') == 'table':
                table_body = item.get('table_body', '')
                if table_body:
                    parts.append(f"```html\n{table_body}\n```")
        
        return '\n\n'.join(parts)


# 创建全局错误处理器实例
error_handler = ErrorHandler()
fallback_strategy = FallbackStrategy()
