"""
评估系统测试脚本
"""

import json
import tempfile
from pathlib import Path

from .content_extractor import ContentExtractor
from .text_metric import NED
from .table_recognition_metric import TEDS
from .file_matcher import FileMatcher
from .evaluator import Evaluator


def test_content_extractor():
    """测试内容提取器"""
    print("测试内容提取器...")
    
    extractor = ContentExtractor()
    
    test_content = """
# 测试文档

这是一段测试文本。

<table><tr><td>列1</td><td>列2</td></tr><tr><td>数据1</td><td>数据2</td></tr></table>

这是另一段文本。

<table><tr><td>表格2</td></tr></table>

结束文本。
"""
    
    result = extractor.extract_from_content(test_content)
    
    assert result['table_count'] == 2, f"期望2个表格，实际{result['table_count']}"
    assert len(result['text_content']) > 0, "文本内容不应为空"
    
    print("✓ 内容提取器测试通过")


def test_text_metric():
    """测试文本评估指标"""
    print("测试文本评估指标...")
    
    ned = NED()
    
    # 测试相同文本
    score1 = ned("相同的文本", "相同的文本")
    assert score1 == 1.0, f"相同文本分数应为1.0，实际{score1}"
    
    # 测试不同文本
    score2 = ned("不同的文本", "相同的文本")
    assert 0 <= score2 < 1.0, f"不同文本分数应在0-1之间，实际{score2}"
    
    # 测试空文本
    score3 = ned("", "")
    assert score3 == 1.0, f"空文本分数应为1.0，实际{score3}"
    
    print("✓ 文本评估指标测试通过")


def test_table_metric():
    """测试表格评估指标"""
    print("测试表格评估指标...")
    
    teds = TEDS()
    
    # 测试相同表格
    table_html = "<html><body><table><tr><td>A</td><td>B</td></tr></table></body></html>"
    score1 = teds(table_html, table_html)
    assert score1 == 1.0, f"相同表格分数应为1.0，实际{score1}"
    
    # 测试不同表格
    table_html2 = "<html><body><table><tr><td>C</td><td>D</td></tr></table></body></html>"
    score2 = teds(table_html2, table_html)
    assert 0 <= score2 < 1.0, f"不同表格分数应在0-1之间，实际{score2}"
    
    print("✓ 表格评估指标测试通过")


def test_file_matcher():
    """测试文件匹配器"""
    print("测试文件匹配器...")
    
    # 使用实际的truth和output目录
    truth_dir = Path("truth")
    output_dir = Path("output")
    
    if not truth_dir.exists() or not output_dir.exists():
        print("⚠ 跳过文件匹配器测试（目录不存在）")
        return
    
    matcher = FileMatcher(truth_dir, output_dir)
    report = matcher.get_match_report()
    
    assert 'total_truth_files' in report, "报告应包含truth文件数"
    assert 'total_output_files' in report, "报告应包含output文件数"
    assert 'matched_pairs' in report, "报告应包含匹配对数"
    
    print("✓ 文件匹配器测试通过")


def test_evaluator():
    """测试评估器"""
    print("测试评估器...")
    
    # 创建临时测试文件
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试文件
        truth_content = """
# 测试文档

这是测试文本。

<table><tr><td>A</td><td>B</td></tr><tr><td>1</td><td>2</td></tr></table>
"""
        
        output_content = """
# 测试文档

这是测试文本。

<table><tr><td>A</td><td>B</td></tr><tr><td>1</td><td>2</td></tr></table>
"""
        
        truth_file = temp_path / "truth.md"
        output_file = temp_path / "output.md"
        
        truth_file.write_text(truth_content, encoding='utf-8')
        output_file.write_text(output_content, encoding='utf-8')
        
        # 测试评估
        evaluator = Evaluator()
        result = evaluator.evaluate_file_pair(truth_file, output_file)
        
        assert 'error' not in result, f"评估不应出错: {result.get('error')}"
        assert 'overall_score' in result, "结果应包含综合分数"
        assert result['overall_score']['weighted_score'] > 0.9, "相同内容分数应很高"
        
    print("✓ 评估器测试通过")


def test_integration():
    """集成测试"""
    print("测试集成功能...")
    
    # 使用实际文件进行集成测试
    truth_file = Path("truth/data.md")
    output_file = Path("output/data.md")
    
    if not truth_file.exists() or not output_file.exists():
        print("⚠ 跳过集成测试（测试文件不存在）")
        return
    
    evaluator = Evaluator()
    result = evaluator.evaluate_file_pair(truth_file, output_file)
    
    assert 'error' not in result, f"集成测试不应出错: {result.get('error')}"
    assert 'text_evaluation' in result, "结果应包含文本评估"
    assert 'table_evaluation' in result, "结果应包含表格评估"
    assert 'overall_score' in result, "结果应包含综合分数"
    
    # 验证分数范围
    text_score = result['text_evaluation']['ned_score']
    table_score = result['table_evaluation']['avg_teds_score']
    overall_score = result['overall_score']['weighted_score']
    
    assert 0 <= text_score <= 1, f"文本分数应在0-1之间: {text_score}"
    assert 0 <= table_score <= 1, f"表格分数应在0-1之间: {table_score}"
    assert 0 <= overall_score <= 1, f"综合分数应在0-1之间: {overall_score}"
    
    print("✓ 集成测试通过")


def run_all_tests():
    """运行所有测试"""
    print("开始运行评估系统测试...")
    print("=" * 50)
    
    try:
        test_content_extractor()
        test_text_metric()
        test_table_metric()
        test_file_matcher()
        test_evaluator()
        test_integration()
        
        print("=" * 50)
        print("✅ 所有测试通过！评估系统工作正常。")
        
    except Exception as e:
        print("=" * 50)
        print(f"❌ 测试失败: {str(e)}")
        raise


if __name__ == "__main__":
    run_all_tests()
