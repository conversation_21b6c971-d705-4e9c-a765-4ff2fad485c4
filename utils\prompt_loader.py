"""
提示词加载器模块
"""

import re
from pathlib import Path
from typing import Dict, Any
from utils.logger import get_logger
from config import PROMPTS_DIR

logger = get_logger(__name__)


class PromptLoader:
    """提示词加载器"""

    def __init__(self, prompts_dir: Path = None):
        """
        初始化提示词加载器

        Args:
            prompts_dir: 提示词目录路径，默认使用配置文件中的路径
        """
        self.prompts_dir = prompts_dir or PROMPTS_DIR
        self.table_merge_file = self.prompts_dir / "table_merge_prompts.md"
        self.table_fix_file = self.prompts_dir / "table_fix_prompts.md"
        self.text_join_file = self.prompts_dir / "text_join_prompts.md"
        self._prompts_cache = {}
        self._load_prompts()

    def _load_prompts(self):
        """加载提示词文件"""
        try:
            # 加载表格合并提示词
            if self.table_merge_file.exists():
                with open(self.table_merge_file, "r", encoding="utf-8") as f:
                    table_content = f.read()
                table_prompts = self._parse_single_prompt_file(table_content)
                self._prompts_cache["table_merge"] = table_prompts
                logger.info(f"成功加载表格合并提示词: {self.table_merge_file}")
            else:
                logger.warning(f"表格合并提示词文件不存在: {self.table_merge_file}")
                self._prompts_cache["table_merge"] = (
                    self._get_fallback_table_merge_prompts()
                )

            # 加载表格修复提示词
            if self.table_fix_file.exists():
                with open(self.table_fix_file, "r", encoding="utf-8") as f:
                    table_fix_content = f.read()
                table_fix_prompts = self._parse_single_prompt_file(table_fix_content)
                self._prompts_cache["table_fix"] = table_fix_prompts
                logger.info(f"成功加载表格修复提示词: {self.table_fix_file}")
            else:
                logger.warning(f"表格修复提示词文件不存在: {self.table_fix_file}")
                self._prompts_cache["table_fix"] = (
                    self._get_fallback_table_fix_prompts()
                )

            # 加载文本拼接提示词
            if self.text_join_file.exists():
                with open(self.text_join_file, "r", encoding="utf-8") as f:
                    text_content = f.read()
                text_prompts = self._parse_single_prompt_file(text_content)
                self._prompts_cache["text_join"] = text_prompts
                logger.info(f"成功加载文本拼接提示词: {self.text_join_file}")
            else:
                logger.warning(f"文本拼接提示词文件不存在: {self.text_join_file}")
                self._prompts_cache["text_join"] = {
                    "system": self._get_fallback_text_join_system(),
                    "user": self._get_fallback_text_join_user(),
                }

        except Exception as e:
            logger.error(f"加载提示词文件失败: {e}")
            # 使用降级提示词
            self._prompts_cache = {
                "table_merge": {
                    "system": self._get_fallback_table_merge_system(),
                    "user": self._get_fallback_table_merge_user(),
                },
                "table_fix": {
                    "system": self._get_fallback_table_fix_system(),
                    "user": self._get_fallback_table_fix_user(),
                },
                "text_join": {
                    "system": self._get_fallback_text_join_system(),
                    "user": self._get_fallback_text_join_user(),
                },
            }

    def _parse_prompts(self, content: str):
        """解析提示词内容"""
        # 清空缓存
        self._prompts_cache = {}

        # 解析表格首行合并判断的提示词
        table_merge_system = self._extract_prompt_block(
            content,
            r"## 1\. 表格首行合并判断.*?### 系统提示词.*?```\n(.*?)\n```",
            re.DOTALL,
        )

        table_merge_user = self._extract_prompt_block(
            content, r"### 用户提示词模板.*?```\n(.*?)\n```", re.DOTALL
        )

        # 解析文本拼接判断的提示词
        text_join_system = self._extract_prompt_block(
            content,
            r"## 2\. 文本拼接判断.*?### 系统提示词.*?```\n(.*?)\n```",
            re.DOTALL,
        )

        text_join_user = self._extract_prompt_block(
            content,
            r"## 2\. 文本拼接判断.*?### 用户提示词模板.*?```\n(.*?)\n```",
            re.DOTALL,
        )

        # 存储到缓存
        self._prompts_cache = {
            "table_merge": {"system": table_merge_system, "user": table_merge_user},
            "text_join": {"system": text_join_system, "user": text_join_user},
        }

        logger.debug(f"解析到 {len(self._prompts_cache)} 类提示词")

    def _parse_single_prompt_file(self, content: str) -> Dict[str, str]:
        """
        解析单个提示词文件

        Args:
            content: 文件内容

        Returns:
            包含system和user提示词的字典
        """
        # 提取系统提示词
        system_pattern = r"## 系统提示词.*?```\n(.*?)\n```"
        system_match = re.search(system_pattern, content, re.DOTALL)
        system_prompt = system_match.group(1).strip() if system_match else ""

        # 提取用户提示词模板
        user_pattern = r"## 用户提示词模板.*?```\n(.*?)\n```"
        user_match = re.search(user_pattern, content, re.DOTALL)
        user_prompt = user_match.group(1).strip() if user_match else ""

        return {"system": system_prompt, "user": user_prompt}

    def _extract_prompt_block(self, content: str, pattern: str, flags: int = 0) -> str:
        """提取提示词块"""
        match = re.search(pattern, content, flags)
        if match:
            return match.group(1).strip()
        return ""

    def get_table_merge_prompts(self) -> Dict[str, str]:
        """
        获取表格合并判断的提示词

        Returns:
            包含system和user提示词的字典
        """
        return self._prompts_cache.get(
            "table_merge",
            {
                "system": self._get_fallback_table_merge_system(),
                "user": self._get_fallback_table_merge_user(),
            },
        )

    def get_table_fix_prompts(self) -> Dict[str, str]:
        """
        获取表格修复的提示词

        Returns:
            包含system和user提示词的字典
        """
        return self._prompts_cache.get(
            "table_fix",
            {
                "system": self._get_fallback_table_fix_system(),
                "user": self._get_fallback_table_fix_user(),
            },
        )

    def get_text_join_prompts(self) -> Dict[str, str]:
        """
        获取文本拼接判断的提示词

        Returns:
            包含system和user提示词的字典
        """
        return self._prompts_cache.get(
            "text_join",
            {
                "system": self._get_fallback_text_join_system(),
                "user": self._get_fallback_text_join_user(),
            },
        )

    def format_table_merge_prompt(self, prev_text: str, next_text: str) -> str:
        """
        格式化表格合并判断的用户提示词

        Args:
            prev_text: 上一页表格文本
            next_text: 下一页表格文本

        Returns:
            格式化后的提示词
        """
        prompts = self.get_table_merge_prompts()
        user_template = prompts.get("user", self._get_fallback_table_merge_user())

        return user_template.format(prev_text=prev_text, next_text=next_text)

    def format_text_join_prompt(self, prev_content: str, next_content: str) -> str:
        """
        格式化文本拼接判断的用户提示词

        Args:
            prev_content: 上一页文本内容
            next_content: 下一页文本内容

        Returns:
            格式化后的提示词
        """
        prompts = self.get_text_join_prompts()
        user_template = prompts.get("user", self._get_fallback_text_join_user())

        return user_template.format(
            prev_content=prev_content, next_content=next_content
        )

    def format_table_fix_prompt(self, tables: list) -> str:
        """
        格式化表格修复的用户提示词

        Args:
            tables: 表格HTML列表

        Returns:
            格式化后的提示词
        """
        prompts = self.get_table_fix_prompts()
        user_template = prompts.get("user", self._get_fallback_table_fix_user())

        # 格式化多个表格内容
        tables_content = ""
        for i, table_html in enumerate(tables, 1):
            tables_content += f"<table>\n{table_html}\n</table>\n\n"

        return user_template.format(tables_content=tables_content.strip())

    def reload_prompts(self):
        """重新加载提示词文件"""
        logger.info("重新加载提示词文件")
        self._load_prompts()

    # 降级提示词（当文件加载失败时使用）
    def _get_fallback_table_merge_system(self) -> str:
        """表格合并系统提示词降级版本"""
        return """你是一个专业的表格分析专家。你需要判断两个表格片段是否需要合并首行。

判断标准：
1. 上一页表格的最后一行是否有空白单元格
2. 下一页表格的第一行是否在对应位置填补了数据
3. 数据在语义上是否连贯，属于同一记录
4. 列结构是否匹配

请只回答 YES 或 NO，不要解释。"""

    def _get_fallback_table_merge_user(self) -> str:
        """表格合并用户提示词降级版本"""
        return """请分析以下两个表格片段：

上一页表格最后几行：
{prev_text}

下一页表格前几行：
{next_text}

问题：下一页的第一行是否是上一页最后一行数据的延续，需要合并？

回答："""

    def _get_fallback_table_fix_system(self) -> str:
        """表格修复系统提示词降级版本"""
        return """你是一个专业的表格修复专家，专门处理因页面分割而断裂的HTML表格。你的任务是将多个独立的HTML表格合并并修复成一个完整、正确的表格。

核心能力：
1. 合并多个表格片段
2. 修复跨页断裂导致的行数据不完整或错位问题
3. 根据表格逻辑结构修正rowspan和colspan属性
4. 删除重复的表头
5. 确保表格结构的一致性和完整性

输出要求：
- 只输出修复后的完整HTML表格代码
- 不要添加任何解释、说明或其他文字
- 保持原有内容和结构，除非必须修复
- 不要添加CSS样式
- 确保HTML格式正确且可直接使用"""

    def _get_fallback_table_fix_user(self) -> str:
        """表格修复用户提示词降级版本"""
        return """我有一个因页面分割而断裂的表格，现在有数个独立的 HTML 表格数据，请将它们合并并修复。

{tables_content}

具体任务：
1. 合并数个表格 (<table> 标签内的内容)。
2. 修复因跨页断裂导致的行数据不完整或错位问题。
3. 根据表格的逻辑结构，修正不正确的 rowspan 和 colspan。

修复规则：
1. 行合并：将第二个表格的第一行与第一个表格的最后一行进行比对，若它们是同一逻辑行的延续，则将它们合并。
2. rowspan：识别并合并相邻行中同一列内内容相同的单元格。
3. colspan：检查表头和数据行的单元格数量，确保它们与完整的表格结构匹配，并使用 colspan 填充缺失的单元格；每行的 colspan 总数应该相同。
4. 重复表头：如果表头有重复，那么应该删去。

注意：尽量保持原有的内容、结构、除非是必须的修复，否则不要修改单元格内容；无需添加样式。

请以一个完整的、可直接使用的 HTML 代码作为最终输出，无需任何解释或者说明。"""

    def _get_fallback_text_join_system(self) -> str:
        """文本拼接系统提示词降级版本"""
        return """你是一个专业的文本分析专家。你需要判断两段文本是否应该拼接为一个完整的段落。

判断标准：
1. 上一段文本是否以不完整的句子结尾（如没有句号、分号等）
2. 下一段文本是否像是上一段的延续
3. 语义上是否连贯
4. 是否属于同一个完整的表达

请只回答 YES 或 NO，不要解释。"""

    def _get_fallback_text_join_user(self) -> str:
        """文本拼接用户提示词降级版本"""
        return """请分析以下两段文本：

上一段文本：
"{prev_content}"

下一段文本：
"{next_content}"

问题：这两段文本是否应该拼接为一个完整的段落？

回答："""


# 创建全局提示词加载器实例
prompt_loader = PromptLoader()
