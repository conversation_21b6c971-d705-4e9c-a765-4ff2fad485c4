"""
表格内容提取工具 - 提供表格HTML到文本转换和内容提取功能
"""

import re
from typing import List, Dict, Any, Optional
from bs4 import BeautifulSoup


class TableContentExtractor:
    """表格内容提取器"""
    
    def __init__(self):
        """初始化表格内容提取器"""
        pass
    
    def parse_table_to_text(self, table_html: str) -> str:
        """
        将表格HTML转换为纯文本用于NED计算
        
        Args:
            table_html: HTML表格字符串
            
        Returns:
            表格的纯文本表示
        """
        try:
            soup = BeautifulSoup(table_html, 'html.parser')
            table = soup.find('table')
            
            if not table:
                return ""
            
            text_rows = []
            for tr in table.find_all('tr'):
                row_texts = []
                for cell in tr.find_all(['td', 'th']):
                    cell_text = self.normalize_cell_content(cell.get_text(strip=True))
                    row_texts.append(cell_text)
                
                if row_texts:  # 只添加非空行
                    text_rows.append('\t'.join(row_texts))
            
            return '\n'.join(text_rows)
            
        except Exception as e:
            print(f"表格转文本失败: {e}")
            return ""
    
    def extract_table_rows(self, table_html: str, max_rows: int = 5) -> List[List[str]]:
        """
        提取指定行数的表格数据
        
        Args:
            table_html: HTML表格字符串
            max_rows: 最大提取行数
            
        Returns:
            表格行数据列表
        """
        try:
            soup = BeautifulSoup(table_html, 'html.parser')
            table = soup.find('table')
            
            if not table:
                return []
            
            rows = []
            for i, tr in enumerate(table.find_all('tr')):
                if i >= max_rows:
                    break
                
                row = []
                for cell in tr.find_all(['td', 'th']):
                    cell_text = self.normalize_cell_content(cell.get_text(strip=True))
                    row.append(cell_text)
                
                if row:  # 只添加非空行
                    rows.append(row)
            
            return rows
            
        except Exception as e:
            print(f"提取表格行失败: {e}")
            return []
    
    def normalize_cell_content(self, cell_text: str) -> str:
        """
        标准化单元格内容
        
        Args:
            cell_text: 原始单元格文本
            
        Returns:
            标准化后的文本
        """
        if not cell_text:
            return ""
        
        # 移除多余的空白字符
        cell_text = re.sub(r'\s+', ' ', cell_text.strip())
        
        # 移除特殊字符（可选）
        # cell_text = re.sub(r'[^\w\s\u4e00-\u9fff]', '', cell_text)
        
        return cell_text
    
    def extract_table_structure(self, table_html: str) -> Dict[str, Any]:
        """
        提取表格结构信息
        
        Args:
            table_html: HTML表格字符串
            
        Returns:
            表格结构信息字典
        """
        try:
            soup = BeautifulSoup(table_html, 'html.parser')
            table = soup.find('table')
            
            if not table:
                return {
                    'rows': 0,
                    'cols': 0,
                    'has_header': False,
                    'cells': []
                }
            
            rows = table.find_all('tr')
            row_count = len(rows)
            col_count = 0
            has_header = False
            cells = []
            
            for i, tr in enumerate(rows):
                row_cells = []
                current_cols = 0
                
                for cell in tr.find_all(['td', 'th']):
                    if cell.name == 'th':
                        has_header = True
                    
                    cell_info = {
                        'text': self.normalize_cell_content(cell.get_text(strip=True)),
                        'tag': cell.name,
                        'colspan': int(cell.get('colspan', 1)),
                        'rowspan': int(cell.get('rowspan', 1))
                    }
                    
                    row_cells.append(cell_info)
                    current_cols += cell_info['colspan']
                
                cells.append(row_cells)
                col_count = max(col_count, current_cols)
            
            return {
                'rows': row_count,
                'cols': col_count,
                'has_header': has_header,
                'cells': cells
            }
            
        except Exception as e:
            print(f"提取表格结构失败: {e}")
            return {
                'rows': 0,
                'cols': 0,
                'has_header': False,
                'cells': []
            }
    
    def get_table_signature(self, table_html: str, max_rows: int = 3) -> str:
        """
        获取表格签名，用于快速比较
        
        Args:
            table_html: HTML表格字符串
            max_rows: 用于生成签名的最大行数
            
        Returns:
            表格签名字符串
        """
        try:
            rows = self.extract_table_rows(table_html, max_rows)
            if not rows:
                return ""
            
            # 将前几行的内容连接成签名
            signature_parts = []
            for row in rows:
                signature_parts.append('|'.join(row))
            
            signature = '\n'.join(signature_parts)
            
            # 可选：计算哈希值以获得更短的签名
            # import hashlib
            # return hashlib.md5(signature.encode()).hexdigest()
            
            return signature
            
        except Exception as e:
            print(f"生成表格签名失败: {e}")
            return ""
    
    def compare_table_structures(self, table1_html: str, table2_html: str) -> Dict[str, Any]:
        """
        比较两个表格的结构
        
        Args:
            table1_html: 第一个表格的HTML
            table2_html: 第二个表格的HTML
            
        Returns:
            结构比较结果
        """
        struct1 = self.extract_table_structure(table1_html)
        struct2 = self.extract_table_structure(table2_html)
        
        return {
            'same_dimensions': (struct1['rows'] == struct2['rows'] and 
                              struct1['cols'] == struct2['cols']),
            'both_have_header': struct1['has_header'] and struct2['has_header'],
            'row_diff': abs(struct1['rows'] - struct2['rows']),
            'col_diff': abs(struct1['cols'] - struct2['cols']),
            'table1_structure': struct1,
            'table2_structure': struct2
        }
    
    def extract_table_text_for_matching(self, table_html: str, max_rows: int = 5) -> str:
        """
        提取表格文本用于匹配算法
        
        Args:
            table_html: HTML表格字符串
            max_rows: 最大行数
            
        Returns:
            用于匹配的文本字符串
        """
        rows = self.extract_table_rows(table_html, max_rows)
        if not rows:
            return ""
        
        # 将表格转换为简洁的文本格式
        text_lines = []
        for row in rows:
            # 过滤空单元格，用空格连接
            filtered_cells = [cell for cell in row if cell.strip()]
            if filtered_cells:
                text_lines.append(' '.join(filtered_cells))
        
        return '\n'.join(text_lines)


def main():
    """测试函数"""
    extractor = TableContentExtractor()
    
    # 测试表格
    test_table = """
    <table>
        <tr><th>姓名</th><th>年龄</th><th>城市</th></tr>
        <tr><td>张三</td><td>25</td><td>北京</td></tr>
        <tr><td>李四</td><td>30</td><td>上海</td></tr>
        <tr><td>王五</td><td>28</td><td>广州</td></tr>
    </table>
    """
    
    print("表格转文本:")
    print(extractor.parse_table_to_text(test_table))
    
    print("\n提取前3行:")
    rows = extractor.extract_table_rows(test_table, 3)
    for i, row in enumerate(rows):
        print(f"行{i+1}: {row}")
    
    print("\n表格结构:")
    structure = extractor.extract_table_structure(test_table)
    print(f"行数: {structure['rows']}, 列数: {structure['cols']}, 有表头: {structure['has_header']}")
    
    print("\n表格签名:")
    signature = extractor.get_table_signature(test_table)
    print(signature)


if __name__ == "__main__":
    main()
