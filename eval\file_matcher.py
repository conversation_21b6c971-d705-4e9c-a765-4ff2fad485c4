"""
文件匹配器 - 匹配truth和output目录中的对应文件
"""

import re
from pathlib import Path
from typing import List, Tuple, Dict, Optional, Set


class FileMatcher:
    """文件匹配器，用于匹配truth和output目录中的对应文件"""
    
    def __init__(self, truth_dir: str | Path, output_dir: str | Path):
        """
        初始化文件匹配器
        
        Args:
            truth_dir: 真实标准文件目录
            output_dir: 输出文件目录
        """
        self.truth_dir = Path(truth_dir)
        self.output_dir = Path(output_dir)
        
        # 用于清理文件名的正则表达式
        self.timestamp_pattern = re.compile(r'-?\d{10,13}')  # 时间戳
        self.suffix_pattern = re.compile(r'_content_list|_output|_result')  # 常见后缀
    
    def find_file_pairs(self, file_extension: str = '.md') -> List[Tuple[Path, Path]]:
        """
        查找匹配的文件对
        
        Args:
            file_extension: 文件扩展名
            
        Returns:
            匹配的文件对列表 [(truth_file, output_file), ...]
        """
        # 获取所有文件
        truth_files = self._get_files(self.truth_dir, file_extension)
        output_files = self._get_files(self.output_dir, file_extension)
        
        if not truth_files:
            raise ValueError(f"在 {self.truth_dir} 中未找到 {file_extension} 文件")
        
        if not output_files:
            raise ValueError(f"在 {self.output_dir} 中未找到 {file_extension} 文件")
        
        # 匹配文件
        file_pairs = []
        matched_output_files = set()
        
        for truth_file in truth_files:
            output_file = self._find_matching_file(truth_file, output_files)
            if output_file:
                file_pairs.append((truth_file, output_file))
                matched_output_files.add(output_file)
        
        # 报告未匹配的文件
        unmatched_truth = [f for f in truth_files if not any(f == pair[0] for pair in file_pairs)]
        unmatched_output = [f for f in output_files if f not in matched_output_files]
        
        if unmatched_truth or unmatched_output:
            print(f"警告: 发现未匹配的文件")
            if unmatched_truth:
                print(f"  未匹配的truth文件: {[f.name for f in unmatched_truth]}")
            if unmatched_output:
                print(f"  未匹配的output文件: {[f.name for f in unmatched_output]}")
        
        return file_pairs
    
    def _get_files(self, directory: Path, extension: str) -> List[Path]:
        """
        获取目录中指定扩展名的所有文件
        
        Args:
            directory: 目录路径
            extension: 文件扩展名
            
        Returns:
            文件路径列表
        """
        if not directory.exists():
            return []
        
        pattern = f"*{extension}"
        return sorted(directory.glob(pattern))
    
    def _find_matching_file(self, truth_file: Path, output_files: List[Path]) -> Optional[Path]:
        """
        为truth文件找到匹配的output文件
        
        Args:
            truth_file: truth文件路径
            output_files: output文件列表
            
        Returns:
            匹配的output文件路径，如果没找到则返回None
        """
        truth_name = truth_file.stem
        
        # 1. 精确匹配
        for output_file in output_files:
            if output_file.stem == truth_name:
                return output_file
        
        # 2. 清理后匹配
        truth_clean = self._clean_filename(truth_name)
        
        for output_file in output_files:
            output_clean = self._clean_filename(output_file.stem)
            if output_clean == truth_clean:
                return output_file
        
        # 3. 部分匹配（包含关系）
        for output_file in output_files:
            output_clean = self._clean_filename(output_file.stem)
            if truth_clean in output_clean or output_clean in truth_clean:
                return output_file
        
        # 4. 模糊匹配（基于相似度）
        best_match = None
        best_score = 0.0
        
        for output_file in output_files:
            output_clean = self._clean_filename(output_file.stem)
            score = self._compute_similarity(truth_clean, output_clean)
            if score > best_score and score > 0.6:  # 相似度阈值
                best_score = score
                best_match = output_file
        
        return best_match
    
    def _clean_filename(self, filename: str) -> str:
        """
        清理文件名，移除时间戳和常见后缀
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的文件名
        """
        # 移除时间戳
        cleaned = self.timestamp_pattern.sub('', filename)
        
        # 移除常见后缀
        cleaned = self.suffix_pattern.sub('', cleaned)
        
        # 移除多余的连字符和下划线
        cleaned = re.sub(r'[-_]+', '-', cleaned)
        cleaned = cleaned.strip('-_')
        
        return cleaned.lower()
    
    def _compute_similarity(self, str1: str, str2: str) -> float:
        """
        计算两个字符串的相似度
        
        Args:
            str1: 字符串1
            str2: 字符串2
            
        Returns:
            相似度分数 (0-1)
        """
        if not str1 or not str2:
            return 0.0
        
        if str1 == str2:
            return 1.0
        
        # 使用简单的字符集交集比例作为相似度
        set1 = set(str1)
        set2 = set(str2)
        
        intersection = len(set1 & set2)
        union = len(set1 | set2)
        
        return intersection / union if union > 0 else 0.0
    
    def get_match_report(self, file_extension: str = '.md') -> Dict[str, any]:
        """
        生成文件匹配报告
        
        Args:
            file_extension: 文件扩展名
            
        Returns:
            匹配报告字典
        """
        try:
            file_pairs = self.find_file_pairs(file_extension)
            
            truth_files = self._get_files(self.truth_dir, file_extension)
            output_files = self._get_files(self.output_dir, file_extension)
            
            matched_truth = {pair[0] for pair in file_pairs}
            matched_output = {pair[1] for pair in file_pairs}
            
            return {
                'total_truth_files': len(truth_files),
                'total_output_files': len(output_files),
                'matched_pairs': len(file_pairs),
                'unmatched_truth_files': len(truth_files) - len(matched_truth),
                'unmatched_output_files': len(output_files) - len(matched_output),
                'match_rate': len(file_pairs) / len(truth_files) if truth_files else 0.0,
                'file_pairs': [(str(t), str(o)) for t, o in file_pairs]
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'total_truth_files': 0,
                'total_output_files': 0,
                'matched_pairs': 0,
                'match_rate': 0.0
            }


def main():
    """测试函数"""
    # 使用项目中的实际目录进行测试
    truth_dir = Path("truth")
    output_dir = Path("output")
    
    if not truth_dir.exists() or not output_dir.exists():
        print("测试目录不存在，创建示例...")
        return
    
    matcher = FileMatcher(truth_dir, output_dir)
    
    # 生成匹配报告
    report = matcher.get_match_report()
    
    print("文件匹配报告:")
    print("-" * 40)
    print(f"Truth文件总数: {report['total_truth_files']}")
    print(f"Output文件总数: {report['total_output_files']}")
    print(f"匹配的文件对: {report['matched_pairs']}")
    print(f"匹配率: {report['match_rate']:.2%}")
    
    if 'file_pairs' in report:
        print("\n匹配的文件对:")
        for truth, output in report['file_pairs']:
            print(f"  {Path(truth).name} <-> {Path(output).name}")


if __name__ == "__main__":
    main()
